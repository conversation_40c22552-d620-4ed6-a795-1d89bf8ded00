/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Response } from 'express';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

interface ResponseShape<T> {
  statusCode: number;
  status: string;
  data?: T;
  message?: string;
}

@Injectable()
export class ResponseInterceptor<T>
  implements NestInterceptor<T, ResponseShape<T>>
{
  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<ResponseShape<T>> {
    const response: Response = context.switchToHttp().getResponse();
    const statusCode = response.statusCode;

    return next.handle().pipe(
      map((data: any) => {
        return {
          statusCode: statusCode,
          status: 'success',
          data,
          message: 'Request was successful',
        };
      }),
      catchError((error) => {
        const status =
          error instanceof HttpException
            ? error.getStatus()
            : HttpStatus.INTERNAL_SERVER_ERROR;

        let message = 'Internal Server Error';

        if (error instanceof HttpException) {
          const errorResponse = error.getResponse();

          if (typeof errorResponse === 'string') {
            message = errorResponse;
          } else if (
            typeof errorResponse === 'object' &&
            errorResponse !== null
          ) {
            message = (errorResponse as any).message || error.message;
          }
        } else {
          message = error.message || 'Internal Server Error';
        }

        return throwError(
          () =>
            new HttpException(
              {
                statusCode: status,
                status: 'error',
                message: message,
                data: null,
              },
              status,
            ),
        );
      }),
    );
  }
}
