/* eslint-disable @typescript-eslint/no-unsafe-argument */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  UseGuards,
  Logger,
  Query,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/common/guards/jwt.guard';
import { PaymentService } from './payment.service';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { InitiatePaymentDto } from './dto/initiate.dto';
import { WebhookService } from './campay/webhook.service';
import { PaginationQueryDto } from 'src/common/dto/pagination.query.do';
import { Roles } from 'src/common/decorators/roles.decorator';
import { User } from 'src/common/decorators/user.decorator';
import { RolesGuard } from 'src/common/guards/roles.guard';

@ApiTags('Payments')
@Controller('payment')
export class PaymentController {
  private logger = new Logger(PaymentController.name);

  constructor(
    private readonly paymentService: PaymentService,
    private readonly webhookService: WebhookService,
  ) {}

  @Get('transaction/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get payment methods for a transaction' })
  @ApiResponse({
    status: 200,
    description: 'Returns available payment methods',
  })
  @ApiResponse({ status: 404, description: 'Transaction not found' })
  async getPaymentMethods(@Param('id', ParseIntPipe) id: number) {
    return this.paymentService.getPaymentMethods(id);
  }

  @Post('initiate')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Initiate payment for a transaction' })
  @ApiResponse({ status: 200, description: 'Payment initiated successfully' })
  @ApiResponse({ status: 404, description: 'Transaction not found' })
  @ApiResponse({ status: 400, description: 'Invalid transaction state' })
  async initiatePayment(
    @Body() initiatePaymentDto: InitiatePaymentDto,
    @User('id') userId: number,
  ) {
    return this.paymentService.initiate(
      initiatePaymentDto.transactionId,
      userId,
    );
  }

  @Post('/campay/webhook')
  async webhook(@Body() payload: any) {
    const { reference, signature } = payload;
    this.logger.log(`Received callback for transaction: ${reference}`);
    const isValidSignature = this.webhookService.validateSignature(signature);
    if (!isValidSignature) {
      this.logger.warn(`Invalid signature for transaction: ${reference}`);
      return { success: false, message: 'Invalid signature' };
    }
    return this.webhookService.handleWebhook(payload);
  }
  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'super_admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all payments' })
  @ApiResponse({ status: 200, description: 'Returns all payments' })
  @ApiResponse({ status: 404, description: 'No payments found' })
  async getAll(@Query() query: PaginationQueryDto) {
    return this.paymentService.getAll(query);
  }
}
