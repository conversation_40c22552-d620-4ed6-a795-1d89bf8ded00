/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { Injectable, Logger } from '@nestjs/common';
import {
  EscrowRepository,
  PaymentRepository,
  TransactionRepository,
} from 'src/common/repository';
import { WebhookPayload } from './webhook.schema';
import { TransactionNotificationService } from './transaction-notification.service';
import { verify } from 'jsonwebtoken';

@Injectable()
export class WebhookService {
  private logger = new Logger(WebhookService.name);
  private readonly webhookKey = process.env.CAMPAY_WEBHOOK_SECRET;

  constructor(
    private readonly escrow: EscrowRepository,
    private readonly transactionNotification: TransactionNotificationService,
    private readonly paymentRepository: PaymentRepository,
    private readonly transactionRepository: TransactionRepository,
  ) {}

  async handleWebhook(payload: WebhookPayload) {
    this.logger.log(`Webhook received:`);

    try {
      const { reference, status, endpoint, external_reference } = payload;

      this.logger.log(
        `Processing webhook - reference: ${reference}, external_reference: ${external_reference}, status: ${status}, endpoint: ${endpoint}`,
      );

      const payment = await this.paymentRepository.findByPaymentId(reference);

      if (!payment) {
        this.logger.warn(`payment not found for reference: ${reference}`);
        return {
          success: false,
          message: `payment not found: ${reference}`,
        };
      }

      if (endpoint === 'collect') {
        if (status === 'SUCCESSFUL') {
          this.paymentRepository
            .updateByPaymentId(reference, {
              status: 'completed',
            })
            .catch((err) => this.logger.error('Failed to update payment', err));
          this.transactionNotification
            .successPaymentNotification(payment.id)
            .catch(() => {
              this.logger.log('Payment notification sent');
            });
          if (payment.transactionId) {
            await this.escrow.updateByTransactionId(payment.transactionId, {
              status: 'funds_recieved',
            });
          }

          // update transaction to active
          if (payment.transactionId) {
            await this.transactionRepository.update(payment.transactionId, {
              status: 'pending',
            });
            //send active transaction notification
            // this.transactionNotification
            //   .se(payment.transactionId)
            //   .catch(() => {
            //     this.logger.log('Active Transaction notification sent');
            //   });
            this.logger.log('Active Transaction');
          }
        }

        if (status === 'FAILED') {
          await this.paymentRepository.updateByPaymentId(reference, {
            status: 'failed',
          });
          this.transactionNotification
            .failedPaymentNotification(payment.id)
            .catch((err) => {
              this.logger.log(JSON.stringify(err));
            });
        }
      }
      if (endpoint === 'withdraw') {
        const isDisbursement = payment.type === 'disbursement';
        const isRefund = payment.type === 'refund';

        if (status === 'SUCCESSFUL') {
          await this.paymentRepository.updateByPaymentId(reference, {
            status: 'completed',
          });

          if (isDisbursement) {
            await this.escrow.updateByTransactionId(payment.transactionId!, {
              status: 'funds_released',
            });

            // update transactions as completed
            await this.transactionRepository.update(payment.transactionId!, {
              status: 'completed',
            });

            this.transactionNotification
              .successDisbursementNotification(payment.id)
              .catch(() => {
                this.logger.log('Disbursement notification sent');
              });

            // notifcation for completed transaction
            this.transactionNotification
              .successTransactionNotification(payment.transactionId!)
              .catch(() => {
                this.logger.log('Transaction notification sent');
              });
          } else if (isRefund) {
            await this.escrow.updateByTransactionId(payment.transactionId!, {
              status: 'cancelled',
            });

            // update transactions as refunded
            await this.transactionRepository.update(payment.transactionId!, {
              status: 'cancelled',
            });

            this.transactionNotification
              .successRefundNotification(payment.id)
              .catch(() => {
                this.logger.log('Refund notification sent');
              });
          }
        }

        if (status === 'FAILED') {
          await this.paymentRepository.updateByPaymentId(reference, {
            status: 'failed',
          });

          if (isDisbursement) {
            this.transactionNotification
              .failedDisbursementNotification(payment.id)
              .catch((err) =>
                this.logger.error('Failed to send notification', err),
              );

            // Update transaction status to failed
            if (payment.transactionId) {
              await this.transactionRepository.update(payment.transactionId, {
                status: 'failed',
              });

              // Update escrow status
              await this.escrow.updateByTransactionId(payment.transactionId, {
                status: 'cancelled',
              });

              this.transactionNotification
                .failedTransactionNotification(payment.transactionId)
                .catch((err) =>
                  this.logger.error('Failed to send notification', err),
                );
            }
          } else if (isRefund) {
            this.transactionNotification
              .failedRefundNotification(payment.id)
              .catch((err) =>
                this.logger.error('Failed to send notification', err),
              );

            // Update escrow status back to previous state
            if (payment.transactionId) {
              await this.escrow.updateByTransactionId(payment.transactionId, {
                status: 'funds_released',
              });
            }
          }
        }
        this.logger.log(
          `Transaction is a ${isDisbursement ? 'disbursement' : 'refund'}: ${reference}`,
        );
      }
    } catch (error) {
      this.logger.error(`Webhook processing error:`, error);
      return {
        success: false,
        message: `Webhook processing failed: ${error.message}`,
      };
    }
  }

  validateSignature(signature: string) {
    try {
      if (!this.webhookKey) {
        this.logger.error('Webhook key not configured');
        return false;
      }

      // Verify JWT token
      const decoded = verify(signature, this.webhookKey);

      return !!decoded;
    } catch (error) {
      this.logger.error(`Failed to validate signature: ${error.message}`);
      return false;
    }
  }
}
