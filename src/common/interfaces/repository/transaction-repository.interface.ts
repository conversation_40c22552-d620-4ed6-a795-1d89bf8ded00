import {
  Escrow,
  File,
  Payment,
  PaymentMethod,
  Transaction,
  User,
} from '../../schemas';
import { TransactionFilter } from '../filters.interface';
import { PaginatedResponse } from '../paginaton';
import { IRepository } from './repository.interface';

export interface ITransactionRepository extends IRepository<Transaction> {
  findByUserId(
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<any> | null>;
  findByReceivedBy(
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<any> | null>;
  findByEscrowId(escrowId: number): Promise<Transaction | null>;
  findByPaymentMethodId(
    paymentMethodId: number,
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<any> | null>;

  findByIdWithUsers(id: number): Promise<{
    transaction: Transaction;
    initiatedByUser: User;
    receivedByUser: User;
    files: File[];
    escrow: Escrow;
    paymentMethod: PaymentMethod;
    payments: Payment[];
  } | null>;
  create(transactionData: Partial<Transaction>): Promise<Transaction>;
  update(
    id: number,
    transactionData: Partial<Transaction>,
  ): Promise<Transaction | null>;
  delete(id: number): Promise<boolean>;
  deleteAndReturn(id: number): Promise<Transaction | null>;
  getStatusCount(userId: number): Promise<Record<string, number>>;
  getRecentTransactions(userId: number): Promise<
    {
      transaction: Transaction;
      initiatedByUser: User;
      receivedByUser: User;
      files: File[];
      escrow: Escrow;
    }[]
  >;
  findByTransactionId(transactionId: string): Promise<{
    transaction: Transaction;
    initiatedByUser: User;
    receivedByUser: User;
    files: File[];
    escrow: Escrow;
    paymentMethod: PaymentMethod;
    payments: Payment[];
  } | null>;
}
