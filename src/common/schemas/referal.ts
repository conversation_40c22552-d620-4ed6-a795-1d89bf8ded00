import {
  serial,
  pgTable,
  integer,
  varchar,
  timestamp,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users } from './users';

export const Referal = pgTable('referal', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').references(() => users.id),
  referredById: integer('referred_by_id').references(() => users.id),
  referralCode: varchar('referral_code', { length: 255 }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const referalRelations = relations(Referal, ({ one }) => ({
  user: one(users, {
    fields: [Referal.userId],
    references: [users.id],
    relationName: 'userReferrals',
  }),
  referredBy: one(users, {
    fields: [Referal.referredById],
    references: [users.id],
    relationName: 'referredUsers',
  }),
}));

export type Referal = typeof Referal.$inferSelect;
export type NewReferal = typeof Referal.$inferInsert;
