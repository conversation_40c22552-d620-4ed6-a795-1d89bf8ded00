import { Payment } from '../../schemas';
import { PaginatedResponse, PaginationQuery } from '../paginaton';

export interface IPaymentRepository {
  create(paymentData: Partial<Payment>): Promise<Payment>;
  findByTransactionId(transactionId: number): Promise<Payment[] | null>;
  findById(id: number): Promise<Payment | null>;
  findAll(query: PaginationQuery): Promise<PaginatedResponse<{
    payment: Payment;
    transaction: any;
  }> | null>;
  updateByPaymentId(
    paymentId: string,
    paymentData: Partial<Payment>,
  ): Promise<Payment>;
  update(id: number, paymentData: Partial<Payment>): Promise<Payment>;
  delete(id: number): Promise<boolean>;
  findByPaymentId(paymentId: string): Promise<Payment | null>;
}
