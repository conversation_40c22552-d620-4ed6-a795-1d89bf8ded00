/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import {
  WebSocketGateway,
  WebSocketServer,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  WsException,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Injectable, Logger, UseGuards } from '@nestjs/common';
import { SocketAuthMiddleware } from 'src/common/middleware/ws.middleware';
import { UserRepository } from 'src/common/repository';
import { WebsocketGuard } from 'src/common/guards/ws.guard';
import { IAdminClient, IWebNotification } from 'src/common/interfaces';
import { NotificationQueueService } from 'src/webpush-notifications/notification-queue.service';

@UseGuards(WebsocketGuard)
@WebSocketGateway({ cors: { origin: '*' }, namespace: 'admin-notification' })
@Injectable()
export class AdminNotification
  implements OnGatewayConnection, OnGatewayDisconnect, OnGatewayInit
{
  private logger = new Logger(AdminNotification.name);
  @WebSocketServer()
  server: Server;

  constructor(
    private usersRepository: UserRepository,
    private readonly notificationQueueService: NotificationQueueService,
  ) {}

  private adminClients: Map<string, IAdminClient> = new Map();

  afterInit(server: Server): void {
    server.use(SocketAuthMiddleware());
  }

  async handleConnection(client: Socket): Promise<void> {
    try {
      const userId = client.data.user?.id as number;

      if (!userId) throw new WsException('User ID not found');

      const user = await this.usersRepository.findById(userId);
      if (!user) throw new WsException('User not found');

      if (user.role !== 'admin' && user.role !== 'super_admin') {
        throw new WsException('Unauthorized');
      }

      client.data.user.id = user.id;
      client.data.user.role = user.role;
      client.data.user.email = user.email;

      this.adminClients.set(client.id, {
        socket: client,
        userId: user.id,
        email: user.email,
      });

      this.logger.log(`Admin connected: ${user.email}`);

      client.emit('connectionStatus', {
        connected: true,
        userId: user.id,
        userType: user.role,
      });

      await this.sendPendingNotifications(user.id, client);
    } catch (error) {
      client.emit('connectionStatus', {
        connected: false,
        error: error instanceof WsException ? error.message : 'Unauthorized',
      });
      client.disconnect(true);
    }
  }

  handleDisconnect(client: Socket): void {
    console.log(`Client disconnected: ${client.id}`);
    this.adminClients.delete(client.id);
  }

  private async sendPendingNotifications(
    userId: number,
    client: Socket,
  ): Promise<void> {
    try {
      const pending =
        await this.notificationQueueService.getPendingNotifications(userId);

      if (pending && pending.length > 0) {
        const unreadNotifications = pending.filter((n) => !n.read);

        if (unreadNotifications.length > 0) {
          client.emit('pendingNotifications', {
            count: unreadNotifications.length,
            notifications: unreadNotifications,
          });

          this.logger.log(
            `Sent ${unreadNotifications.length} pending notifications to admin ${userId}`,
          );
        }
      }
    } catch (error) {
      this.logger.error(
        `Failed to send pending notifications to admin ${userId}:`,
        error,
      );
    }
  }

  private isAdminOnline(adminId: number): boolean {
    for (const [, client] of this.adminClients) {
      if (client.userId === adminId) {
        return true;
      }
    }
    return false;
  }

  async sendNotificationToAdmins(notification: {
    title: string;
    message: string;
    type: 'info' | 'warning' | 'error' | 'success';
    data?: any;
  }): Promise<void> {
    const fullNotification: IWebNotification = {
      id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...notification,
      timestamp: new Date().toISOString(),
      read: false,
    };

    const admins = await this.usersRepository.findAllAdmin();
    admins.forEach((admin) => {
      if (admin.user.id) {
        const isOnline = this.isAdminOnline(admin.user.id);

        if (isOnline) {
          this.adminClients.forEach((client) => {
            if (client.userId === admin.user.id) {
              client.socket.emit('adminNotification', fullNotification);
            }
          });
        } else {
          this.logger.log(
            `Admin ${admin.user.id} is offline, enqueuing notification`,
          );
          this.notificationQueueService
            .enqueue(admin.user.id, notification)
            .catch((err) => {
              this.logger.error(err);
            });
        }
      }
    });
  }

  sendNotificationToAdmin(
    adminId: number,
    notification: {
      title: string;
      message: string;
      type: 'info' | 'warning' | 'error' | 'success';
      data?: any;
    },
  ): void {
    const fullNotification: IWebNotification = {
      id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...notification,
      timestamp: new Date().toISOString(),
      read: false,
    };

    const isOnline = this.isAdminOnline(adminId);

    if (isOnline) {
      this.adminClients.forEach((client) => {
        if (client.userId === adminId) {
          client.socket.emit('adminNotification', fullNotification);
        }
      });
    } else {
      this.notificationQueueService
        .enqueue(adminId, fullNotification)
        .catch((err) => {
          this.logger.error(err);
        });
    }
  }
}
