import { Injectable, Logger } from '@nestjs/common';
import { AdminNotification } from 'src/webpush-notifications/admin-notifiction.gateway';
import { NotificationRepository } from 'src/common/repository';

@Injectable()
export class AdminNotificationService {
  private logger = new Logger(AdminNotificationService.name);
  constructor(
    private readonly webPush: AdminNotification,
    private readonly notificationRepository: NotificationRepository,
  ) {}

  async sendNotificationToAdmins(notification: {
    title: string;
    message: string;
    type: 'info' | 'warning' | 'error' | 'success';
    data?: any;
  }): Promise<void> {
    try {
      await this.webPush.sendNotificationToAdmins(notification);
      this.notificationRepository
        .create({
          title: notification.title,
          message: notification.message,
          type: 'admin',
          category: 'general_updates',
          priority: 'info',
          metadata: notification.data || {},
        })
        .catch((err) => {
          this.logger.error(err);
        });
    } catch (error) {
      this.logger.error(error);
    }
  }
}
