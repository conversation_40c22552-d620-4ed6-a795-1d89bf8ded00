import { Module } from '@nestjs/common';
import { ReportsService } from './reports.service';
import { ReportsController } from './reports.controller';
import { TransactionReportRepository } from 'src/common/repository/transaction-report.repository';
import { AdminNotification } from 'src/webpush-notifications/admin-notifiction.gateway';
import { UserRepository } from 'src/common/repository';

@Module({
  controllers: [ReportsController],
  providers: [
    ReportsService,
    TransactionReportRepository,
    AdminNotification,
    UserRepository,
  ],
})
export class ReportsModule {}
