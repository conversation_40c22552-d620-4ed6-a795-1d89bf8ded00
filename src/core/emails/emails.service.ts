import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import * as ejs from 'ejs';
import { join } from 'path';
import { readFile } from 'fs/promises';

@Injectable()
export class EmailsService {
  private readonly logger = new Logger(EmailsService.name);
  private readonly transporter: nodemailer.Transporter;

  constructor(private readonly configService: ConfigService) {
    this.transporter = nodemailer.createTransport({
      host: this.configService.get<string>('EMAIL_HOST'),
      port: this.configService.get<number>('EMAIL_PORT') ?? 587,
      secure: false, // true if using port 465
      auth: {
        user: this.configService.get<string>('EMAIL_USER'),
        pass: this.configService.get<string>('EMAIL_PASS'),
      },
      tls: { rejectUnauthorized: false },
    });
  }

  // Utility to render EJS templates
  private async renderTemplate(
    templateName: string,
    context: Record<string, any>,
  ): Promise<string> {
    const templatePath = join(
      __dirname,
      '../../../core/emails/templates',
      `${templateName}.ejs`,
    );
    const template = await readFile(templatePath, 'utf8');
    return ejs.render(template, context);
  }

  private async sendEmail(
    to: string,
    subject: string,
    template: string,
    context: Record<string, any>,
  ) {
    try {
      const html = await this.renderTemplate(template, context);

      await this.transporter.sendMail({
        from: `"No Reply" <${this.configService.get('EMAIL_FROM') ?? '<EMAIL>'}>`,
        to,
        subject,
        html,
      });

      this.logger.log(`Email sent to ${to} with subject "${subject}"`);
    } catch (error) {
      this.logger.error(`Failed to send email to ${to}`, error);
    }
  }

  async sendWelcomeEmail(userEmail: string, userName: string): Promise<void> {
    await this.sendEmail(userEmail, 'Welcome to Hold X!', 'welcome', {
      userName,
      platformName: 'Hold X',
      loginUrl: 'https://holdx.com/login',
      unsubscribeUrl: 'https://holdx.com/unsubscribe',
      platformDomain: 'holdx.com',
      additionalMessage: 'Welcome to Hold X!',
    });
  }

  async sendVerificationEmail(
    userEmail: string,
    userName: string,
    verificationToken: string,
  ): Promise<void> {
    await this.sendEmail(
      userEmail,
      'Verify Your Email Address',
      'verify-account',
      {
        userName,
        platformName: 'Hold X',
        verificationToken,
        unsubscribeUrl: 'https://holdx.com/unsubscribe',
        platformDomain: 'holdx.com',
      },
    );
  }

  async sendResetPasswordEmail(
    userEmail: string,
    userName: string,
    resetToken: string,
  ): Promise<void> {
    await this.sendEmail(userEmail, 'Reset Your Password', 'reset-password', {
      userName,
      platformName: 'Hold X',
      resetToken,
      unsubscribeUrl: 'https://holdx.com/unsubscribe',
      platformDomain: 'holdx.com',
    });
  }

  async sendCustomEmail(
    userEmail: string,
    userName: string,
    subject: string,
    message: string,
    additionalMessage?: '',
  ): Promise<void> {
    await this.sendEmail(userEmail, subject, 'custom', {
      userName,
      platformName: 'Hold X',
      title: subject,
      message,
      unsubscribeUrl: 'https://holdx.com/unsubscribe',
      platformDomain: 'holdx.com',
      additionalMessage,
    });
  }

  async sendTransactionCreatedEmail(
    userEmail: string,
    userName: string,
    transactionTitle: string,
    amount: string,
    isInitiator = true,
  ): Promise<void> {
    const subject = isInitiator
      ? 'Transaction Created Successfully'
      : 'New Transaction Request Received';

    const message = isInitiator
      ? `Your transaction "${transactionTitle}" has been created successfully for $${amount}. The receiver will be notified and you will receive updates.`
      : `You have received a new transaction request for "${transactionTitle}" with amount $${amount}. Please review and respond accordingly.`;

    await this.sendCustomEmail(userEmail, userName, subject, message);
  }

  async sendTransactionStatusUpdateEmail(
    userEmail: string,
    userName: string,
    transactionTitle: string,
    status: string,
  ): Promise<void> {
    const statusMessages = {
      completed: 'has been completed successfully',
      disputed: 'is under dispute',
      cancelled: 'has been cancelled',
      expired: 'has expired',
      failed: 'has failed',
    };

    const statusMessage =
      statusMessages[status as keyof typeof statusMessages] ||
      `status has been updated to ${status}`;

    const subject = 'Transaction Status Update';
    const message = `Your transaction "${transactionTitle}" ${statusMessage}.`;

    await this.sendCustomEmail(userEmail, userName, subject, message);
  }

  async sendTransactionCancelledEmail(
    userEmail: string,
    userName: string,
    transactionTitle: string,
    cancelledBy: string,
  ): Promise<void> {
    const subject = 'Transaction Cancelled';
    const message = `The transaction "${transactionTitle}" has been cancelled by ${cancelledBy}.`;

    await this.sendCustomEmail(userEmail, userName, subject, message);
  }

  async sendPaymentSuccessEmail(
    userEmail: string,
    userName: string,
    transactionTitle: string,
    amount: string,
    direction: string,
  ): Promise<void> {
    const subject = 'Payment Successful';
    const message = `Your payment of $${amount} for transaction from ${direction} "${transactionTitle}" has been successfully processed.`;

    await this.sendCustomEmail(userEmail, userName, subject, message);
  }

  async sendPaymentFailedEmail(
    userEmail: string,
    userName: string,
    transactionTitle: string,
    amount: string,
    direction: string,
  ): Promise<void> {
    const subject = 'Payment Failed';
    const message = `Your payment of $${amount} for transaction from ${direction} "${transactionTitle}" has failed. Please try again.`;

    await this.sendCustomEmail(userEmail, userName, subject, message);
  }
}
