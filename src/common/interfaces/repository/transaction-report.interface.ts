import { Report, Transaction, User } from '../../schemas';
import { PaginatedResponse, PaginationQuery } from '../paginaton';

export interface ITransactionReportRepository {
  findByUserId(
    userId: number,
    query: PaginationQuery,
  ): Promise<PaginatedResponse<Report> | null>;

  findAll(query: PaginationQuery): Promise<PaginatedResponse<{
    report: Report;
    transaction: Transaction;
    reporter: User;
  }> | null>;

  findById(id: number): Promise<Report | null>;

  findByTransactionId(transactionId: number): Promise<Report[]>;
  create(reportData: Partial<Report>): Promise<Report>;
  update(id: number, reportData: Partial<Report>): Promise<Report | null>;
  delete(id: number): Promise<boolean>;
}
