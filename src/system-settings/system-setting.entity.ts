import { ApiProperty } from '@nestjs/swagger';

export class SystemSetting {
  @ApiProperty({ description: 'System setting key' })
  key: string;

  @ApiProperty({ description: 'System setting value' })
  value: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;

  constructor(data: Partial<SystemSetting>) {
    Object.assign(this, data);
  }
}
