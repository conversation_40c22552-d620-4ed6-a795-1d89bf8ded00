import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { CreateSystemSettingDto } from './dto/create-system-setting.dto';
import { UpdateSystemSettingDto } from './dto/update-system-setting.dto';
import { SystemSettingsRepository } from 'src/common/repository/system-settings.repository';
import { SystemSetting } from 'src/common/schemas';

@Injectable()
export class SystemSettingsService {
  constructor(
    private readonly systemSettingsRepository: SystemSettingsRepository,
  ) {}
  async create(createSystemSettingDto: CreateSystemSettingDto) {
    try {
      const exist = await this.systemSettingsRepository.get(
        createSystemSettingDto.key,
      );
      if (exist) throw new BadRequestException('Key already exists');
      return this.systemSettingsRepository.create(
        createSystemSettingDto.key,
        createSystemSettingDto.value,
      );
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getAll(): Promise<SystemSetting[]> {
    try {
      return this.systemSettingsRepository.getAll();
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async findOne(key: string) {
    try {
      const setting = await this.systemSettingsRepository.get(key);
      if (!setting) throw new NotFoundException('Setting not found');
      return setting;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async update(key: string, updateSystemSettingDto: UpdateSystemSettingDto) {
    try {
      const setting = await this.systemSettingsRepository.get(key);
      if (!setting) throw new NotFoundException('Setting not found');

      return this.systemSettingsRepository.update(
        key,
        updateSystemSettingDto.value,
      );
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  remove(key: string): Promise<boolean> {
    try {
      return this.systemSettingsRepository.delete(key);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
}
