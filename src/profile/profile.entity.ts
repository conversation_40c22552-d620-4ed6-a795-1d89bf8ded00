import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from 'src/common/entity';

export class AuthUserProfileDto {
  @ApiProperty({ description: 'User ID' })
  id: number;

  @ApiProperty({ description: 'User email address' })
  email: string;

  @ApiProperty({
    description: 'User role',
    enum: ['admin', 'user', 'super_admin'],
    nullable: true,
  })
  role: 'admin' | 'user' | 'super_admin' | null;

  @ApiProperty({ description: 'User full name' })
  name: string;

  @ApiProperty({ description: 'Google ID for OAuth', nullable: true })
  googleId: string | null;

  @ApiProperty({ description: 'User active status' })
  isActive: boolean;

  @ApiProperty({ description: 'User verification status' })
  isVerified: boolean;

  @ApiProperty({ description: 'User deletion status' })
  isDeleted: boolean;

  @ApiProperty({ description: 'User ban status' })
  isBanned: boolean;

  @ApiProperty({ description: 'Ban timestamp' })
  bannedAt: Date;

  @ApiProperty({ description: 'Deletion timestamp' })
  deletedAt: Date;

  @ApiProperty({ description: 'Notification token', nullable: true })
  notificationToken: string | null;

  @ApiProperty({ description: 'Verification timestamp' })
  verifiedAt: Date;

  @ApiProperty({ description: 'Hashed password' })
  password: string;

  @ApiProperty({ description: 'Authentication provider', nullable: true })
  provider: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;
}

export class UserProfileDetailsEntity {
  @ApiProperty({ description: 'Profile ID' })
  id: number;

  @ApiProperty({ description: 'User first name', nullable: true })
  firstName: string;

  @ApiProperty({ description: 'User last name', nullable: true })
  lastName: string;

  @ApiProperty({ description: 'User code', nullable: true })
  code: string;

  @ApiProperty({ description: 'User bio', nullable: true })
  bio: string;

  @ApiProperty({ description: 'User avatar', nullable: true })
  avatar: string;

  @ApiProperty({ description: 'User ID', nullable: true })
  userId: number;

  @ApiProperty({ description: 'Creation timestamp', nullable: true })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp', nullable: true })
  updatedAt: Date;
}

export class CompleteProfileEntity {
  @ApiProperty({ type: UserProfileDetailsEntity })
  profile: UserProfileDetailsEntity;

  @ApiProperty({ type: AuthUserProfileDto })
  user: AuthUserProfileDto;

  constructor(data: Partial<CompleteProfileEntity>) {
    Object.assign(this, data);
  }
}

export class UserProfileEntity extends BaseEntity<CompleteProfileEntity> {
  @ApiProperty({ type: CompleteProfileEntity })
  declare data?: CompleteProfileEntity;
}
