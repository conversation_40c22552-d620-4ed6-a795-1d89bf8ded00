/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import {
  Injectable,
  Inject,
  BadRequestException,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  v2 as cloudinary,
  UploadApiResponse,
  UploadApiErrorResponse,
} from 'cloudinary';
import {
  FileUploadOptions,
  FileUploadResult,
} from 'src/common/interfaces/cloudinary.interface';
import { Readable } from 'stream';

@Injectable()
export class FilesService {
  private logger = new Logger(FilesService.name);
  constructor(
    @Inject('CLOUDINARY') private cloudinaryConfig: typeof cloudinary,
  ) {}

  async uploadFile(
    file: Express.Multer.File,
    options: FileUploadOptions = {},
  ): Promise<FileUploadResult> {
    try {
      // await this.testCloudinaryConnection();
      if (!file) throw new BadRequestException('No file provided');

      const maxSize = 5 * 1024 * 1024;
      if (file.size > maxSize)
        throw new BadRequestException('File size exceeds 5MB limit');
      const uploadOptions = {
        folder: options.folder || 'uploads',
        resource_type: options.resource_type || 'auto',
        overwrite: options.overwrite ?? false,
        quality: options.quality || 'auto',
        ...options,
      };

      return new Promise<FileUploadResult>((resolve, reject) => {
        const uploadStream = this.cloudinaryConfig.uploader.upload_stream(
          uploadOptions,
          (
            error: UploadApiErrorResponse | undefined,
            result: UploadApiResponse | undefined,
          ) => {
            if (error) {
              reject(
                new InternalServerErrorException(
                  `Upload failed: ${error.message}`,
                ),
              );
            } else if (result) {
              resolve({
                public_id: result.public_id,
                url: result.url,
                secure_url: result.secure_url,
                format: result.format,
                width: result.width,
                height: result.height,
                bytes: result.bytes,
                resource_type: result.resource_type,
                created_at: result.created_at,
                original_filename: result.original_filename,
              });
            }
          },
        );

        const bufferStream = new Readable();
        bufferStream.push(file.buffer);
        bufferStream.push(null);
        bufferStream.pipe(uploadStream);
      });
    } catch (error) {
      throw new InternalServerErrorException(
        `File upload failed: ${error.message}`,
      );
    }
  }

  async uploadMultipleFiles(
    files: Express.Multer.File[],
    options: FileUploadOptions = {},
  ): Promise<FileUploadResult[]> {
    if (!files || files.length === 0)
      throw new BadRequestException('No files provided');

    const uploadPromises = files.map((file, index) =>
      this.uploadFile(file, {
        ...options,
        public_id: options.public_id
          ? `${options.public_id}_${index}`
          : undefined,
      }),
    );

    try {
      return await Promise.all(uploadPromises);
    } catch (error) {
      console.log(error);
      throw new InternalServerErrorException('Failed to upload multiple files');
    }
  }

  extractPublicIdFromUrl(cloudinaryUrl: string): string | null {
    try {
      // Remove the base URL and file extension
      const regex =
        /\/(?:image|video|raw)\/upload\/(?:v\d+\/)?(.+)\.[a-zA-Z0-9]+$/;
      const match = cloudinaryUrl.match(regex);
      return match ? match[1] : null;
    } catch (error) {
      console.error('Error extracting public_id:', error);
      return null;
    }
  }

  async deleteFile(
    publicId: string,
  ): Promise<{ success: boolean; result?: any }> {
    try {
      if (!publicId) {
        throw new BadRequestException('Public ID is required');
      }

      const result: any =
        await this.cloudinaryConfig.uploader.destroy(publicId);

      if (result.result === 'ok') {
        this.logger.log('file was deleted');
        return { success: true, result };
      } else if (result.result === 'not found') {
        throw new NotFoundException(
          `File with public_id '${publicId}' not found`,
        );
      } else {
        throw new InternalServerErrorException(
          `Failed to delete file: ${result.result}`,
        );
      }
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        `File deletion failed: ${error.message}`,
      );
    }
  }
}
