import { verify } from 'jsonwebtoken';
import { Socket } from 'socket.io';
import { WsException } from '@nestjs/websockets';

export const SocketAuthMiddleware = () => {
  return (client: Socket, next: (err?: Error) => void) => {
    try {
      const token = client.handshake.auth.token
        ? client.handshake.auth.token
        : client.handshake.headers['authorization'];

      if (!token) {
        return next(new WsException('Authentication token not provided'));
      }

      const payload = verify(token, process.env.JWT_SECRET || '');

      client.data.user = payload;
      next();
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        return next(new WsException('Token has expired'));
      }

      next(new WsException('Invalid authentication token'));
    }
  };
};
