/* eslint-disable @typescript-eslint/no-redundant-type-constituents */
import { Injectable, Inject } from '@nestjs/common';
import { eq } from 'drizzle-orm';
import { Referal, NewReferal } from '../schemas';
import * as schema from '../schemas/referal';
import { users } from '../schemas/users';
import { IReferalRepository } from '../interfaces/repository/referal-repository.interface';
import { DATABASE_CONNECTION } from 'src/configs';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

@Injectable()
export class ReferalRepository implements IReferalRepository {
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}

  async create(referalData: NewReferal): Promise<Referal> {
    const result = await this.db
      .insert(schema.Referal)
      .values({
        ...referalData,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return result[0];
  }

  async findByUserId(userId: number): Promise<Referal | null> {
    const result = await this.db
      .select()
      .from(schema.Referal)
      .where(eq(schema.Referal.userId, userId))
      .limit(1);

    return result[0] || null;
  }

  async findByReferredById(referredById: number): Promise<Referal | null> {
    const result = await this.db
      .select()
      .from(schema.Referal)
      .where(eq(schema.Referal.referredById, referredById))
      .limit(1);

    return result[0] || null;
  }

  async getUserRefarrals(userId: number): Promise<any[]> {
    const result = await this.db
      .select({
        referal: schema.Referal,
        referredUser: users,
      })
      .from(schema.Referal)
      .where(eq(schema.Referal.referredById, userId))
      .leftJoin(users, eq(schema.Referal.userId, users.id));

    return result;
  }

  // Get all referrals made by a specific user (alternative method)
  async getReferredUsers(referredById: number): Promise<Referal[]> {
    const result = await this.db
      .select()
      .from(schema.Referal)
      .where(eq(schema.Referal.referredById, referredById));

    return result;
  }

  async getWhoReferredUser(userId: number): Promise<any | null> {
    const result = await this.db
      .select({
        referal: schema.Referal,
        referrer: users,
      })
      .from(schema.Referal)
      .where(eq(schema.Referal.userId, userId))
      .leftJoin(users, eq(schema.Referal.referredById, users.id))
      .limit(1);

    return result[0] || null;
  }

  async getReferralWithDetails(userId: number): Promise<any | null> {
    const result = await this.db
      .select({
        referal: schema.Referal,
        user: users,
      })
      .from(schema.Referal)
      .where(eq(schema.Referal.userId, userId))
      .innerJoin(users, eq(schema.Referal.userId, users.id))
      .limit(1);

    return result[0] || null;
  }
}
