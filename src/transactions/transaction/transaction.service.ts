import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
  Inject,
  Logger,
} from '@nestjs/common';
import {
  TransactionRepository,
  UserRepository,
  PaymentMethodRepository,
} from 'src/common/repository/';
import { CreateTransactionDto } from './dto/create.dto';
import { Transaction, NewTransaction, User } from 'src/common/schemas';
import * as schema from 'src/common/schemas';
import { EmailsService } from 'src/core/emails/emails.service';
import { NotificationsService } from 'src/core/notifications/notifications.service';
import {
  PaginatedResponse,
  TransactionStatus,
  TransactionFilter,
} from 'src/common/interfaces';

import { FilesService } from 'src/core/files/files.service';
import { FileRepository } from 'src/common/repository';
import { DeclineTransactionDto } from './dto/decline.dto';
import { AcceptTransactionDto } from './dto/accept.dto';
import { EscrowRepository } from 'src/common/repository/escrow.repository';
import { CompleteTransactionDto } from './dto/complete.dto';
import { CampayService } from 'src/core/payment/campay/campay.service';
import { generateOTPCode } from 'src/common/utils';
import { DATABASE_CONNECTION } from 'src/configs';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { PushNotificationService } from 'src/core/notifications/pushnotification.service';
import { TransactionNotificationService } from 'src/core/payment/campay/transaction-notification.service';

@Injectable()
export class TransactionService {
  private logger = new Logger(TransactionService.name);
  constructor(
    private readonly transactionRepository: TransactionRepository,
    private readonly userRepository: UserRepository,
    private readonly paymentMethodRepository: PaymentMethodRepository,
    private readonly emailsService: EmailsService,
    private readonly notificationsService: NotificationsService,
    private readonly fileService: FilesService,
    private readonly fileRepository: FileRepository,
    private readonly escrowRepository: EscrowRepository,
    private readonly campayService: CampayService,
    private readonly pushNotificationService: PushNotificationService,
    private readonly transactionNotification: TransactionNotificationService,
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}

  async createTransaction(
    userId: number,
    createTransactionDto: CreateTransactionDto,
    attachments: Express.Multer.File[],
  ) {
    try {
      const initiator = await this.userRepository.findById(userId);
      if (!initiator) throw new NotFoundException('Initiator user not found');

      const receiver = await this.userRepository.findById(
        +createTransactionDto.sellerId,
      );
      if (!receiver) throw new NotFoundException('Receiver user not found');

      // Validate payment method exists and belongs to user
      const paymentMethod = await this.paymentMethodRepository.findById(
        +createTransactionDto.paymentMethodId,
      );
      if (!paymentMethod)
        throw new NotFoundException('Payment method not found');

      // if payment method is not verified
      if (!paymentMethod.isVerified)
        throw new BadRequestException('Payment method not verified');

      if (paymentMethod.userId !== userId)
        throw new BadRequestException('Payment method does not belong to user');

      const transactionData: NewTransaction = {
        title: createTransactionDto.title,
        description: createTransactionDto.description,
        aggrement: createTransactionDto.aggrement,
        category: createTransactionDto.category,
        deadLine: new Date(createTransactionDto.deadLine),
        transactionId: `HEX-TRN-${generateOTPCode(6)}`,
        initiatedBy: userId,
        receivedBy: +createTransactionDto.sellerId,
        paymentMethodId: +createTransactionDto.paymentMethodId,
        amount: createTransactionDto.amount,
        status: 'pending',
      };

      const transaction = await this.db.transaction(async (tx) => {
        const transactionResult = await tx
          .insert(schema.transactions)
          .values(transactionData)
          .returning();

        const escrowResult = await tx
          .insert(schema.escrow)
          .values({
            transactionId: transactionResult[0].id,
            amount: transactionResult[0].amount,
            status: 'created',
            fee: createTransactionDto.fee,
            deadLine: transactionResult[0].deadLine?.toString(),
          })
          .returning();

        let uploadedFiles: schema.File[] = [];
        if (attachments && attachments.length > 0) {
          const files = await this.fileService.uploadMultipleFiles(attachments);

          const fileData = files.map((file) => ({
            name: file.original_filename,
            url: file.url,
            transactionId: transactionResult[0].id,
          }));

          uploadedFiles = await tx
            .insert(schema.files)
            .values(fileData)
            .returning();
        }

        return {
          transaction: transactionResult[0],
          escrow: escrowResult[0],
          files: uploadedFiles,
        };
      });

      if (
        transaction.transaction &&
        paymentMethod.type === 'mobile_money' &&
        transaction.transaction.status === 'pending'
      ) {
        if (paymentMethod.accountNumber) {
          await this.campayService.collectPayment({
            amount: parseFloat(transaction.transaction.amount),
            phone: paymentMethod.accountNumber.replace('+', '').trim(),
            description: transaction.transaction.title,
            email: initiator.email,
            direction: 'to_escrow',
            transactionId: transaction.transaction.id,
            paymentMethodId: paymentMethod.id,
          });
        }
      }

      // Send notifications and emails
      this.sendTransactionNotifications(
        transaction.transaction,
        initiator,
        // receiver,
      ).catch((err) =>
        this.logger.error('Failed to send payment notification', err),
      );

      return transaction;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getTransactionByTransactionId(transactionId: string, userId: number) {
    try {
      const transaction =
        await this.transactionRepository.findByTransactionId(transactionId);
      if (!transaction) throw new NotFoundException('Transaction not found');

      return {
        ...transaction,
        isInitiator: transaction.transaction.initiatedBy === userId,
      };
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  private async sendTransactionNotifications(
    transaction: Transaction,
    initiator: User,
    // receiver: User,
  ): Promise<void> {
    try {
      // Send email to receiver
      // await this.emailsService.sendTransactionCreatedEmail(
      //   receiver.email,
      //   receiver.name,
      //   transaction.title,
      //   transaction.amount,
      //   false, // not initiator
      // );

      // Send email to initiator
      await this.emailsService.sendTransactionCreatedEmail(
        initiator.email,
        initiator.name,
        transaction.title,
        transaction.amount,
        true, // is initiator
      );

      // Create notification for receiver
      // await this.notificationsService.create({
      //   title: 'New Transaction Request',
      //   message: `${initiator.name} sent you a transaction request for "${transaction.title}"`,
      //   userId: receiver.id,
      //   type: 'personal',
      //   category: 'transactions',
      //   actionRequired: true,
      //   priority: 'pending',
      //   metadata: {
      //     transactionId: transaction.id,
      //     initiatorId: initiator.id,
      //     amount: transaction.amount,
      //     model: 'transaction',
      //   },
      // });

      // Create notification for initiator
      await this.notificationsService.create({
        title: 'Transaction Created',
        message: `Your transaction "${transaction.title}" has been created successfully`,
        userId: initiator.id,
        type: 'personal',
        category: 'transactions',
        priority: 'pending',
        actionRequired: true,
        metadata: {
          transactionId: transaction.id,
          // receiverId: receiver.id,
          amount: transaction.amount,
          model: 'transaction',
        },
      });

      // Send push notifications to both parties
      if (initiator.notificationToken) {
        await this.pushNotificationService.send({
          title: 'New Transaction Request',
          body: `${initiator.name} sent you a transaction request for "${transaction.title}"`,
          pushToken: initiator.notificationToken,
          data: {
            transactionId: transaction.id,
          },
        });
      }
      // if (receiver.notificationToken) {
      //   await this.pushNotificationService.send({
      //     title: 'New Transaction Request',
      //     body: `${initiator.name} sent you a transaction request for "${transaction.title}"`,
      //     pushToken: receiver.notificationToken,
      //     data: {
      //       transactionId: transaction.id,
      //     },
      //   });
      // }
    } catch (error) {
      console.error('Failed to send notifications:', error);
    }
  }

  async getAllTransactions(
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction>> {
    try {
      return await this.transactionRepository.findAll(query);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async declineTransaction(
    userId: number,
    declineTransactionDto: DeclineTransactionDto,
  ) {
    try {
      const transaction = await this.transactionRepository.findById(
        declineTransactionDto.transactionId,
      );
      if (!transaction) throw new NotFoundException('Transaction not found');

      if (transaction.receivedBy !== userId)
        throw new BadRequestException(
          'Only the transaction initiator can decline the transaction',
        );

      if (transaction.status !== 'pending')
        throw new BadRequestException(
          'Only transactions with "pending" status can be declined',
        );

      const updatedTransaction = await this.transactionRepository.update(
        transaction.id,
        { status: 'declined', message: declineTransactionDto.reason },
      );
      if (!updatedTransaction) {
        throw new InternalServerErrorException('Failed to update transaction');
      }

      // disburse funds back to user

      // if (transaction.paymentMethodId) {
      //   const paymentMethod = await this.paymentMethodRepository.findById(
      //     transaction.paymentMethodId,
      //   );

      //   if (!paymentMethod)
      //     throw new NotFoundException('Payment method not found');

      //   if (paymentMethod.type === 'mobile_money') {
      //     if (paymentMethod.accountNumber) {
      //       this.campayService
      //         .disbursement({
      //           amount: +transaction.amount,
      //           phone: paymentMethod.accountNumber.replace('+', '').trim(),
      //           description: transaction.title,
      //           transactionId: transaction.id,
      //           paymentMethodId: paymentMethod.id,
      //           type: 'refund',
      //           direction: 'from_escrow',
      //         })
      //         .then(async (res) => {
      //           if (res.res.reference)
      //             await this.sendStatusUpdateNotifications(
      //               updatedTransaction,
      //               'Declined',
      //               userId,
      //             );
      //         })
      //         .catch((err) => {
      //           console.log(err); // Handle the error as needed
      //         });
      //     }
      //   }
      // }

      return updatedTransaction;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getTransactionById(id: number, userId: number) {
    try {
      const transaction =
        await this.transactionRepository.findByIdWithUsers(id);
      if (!transaction) throw new NotFoundException('Transaction not found');

      return {
        ...transaction,
        isInitiator: transaction.transaction.initiatedBy === userId,
      };
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getUserTransactions(
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction> | null> {
    try {
      const transactions = await this.transactionRepository.findByUserId(
        userId,
        query,
      );

      return transactions;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getReceivedTransactions(
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction> | null> {
    try {
      return await this.transactionRepository.findByReceivedBy(userId, query);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async updateTransactionStatus(
    id: number,
    status: TransactionStatus,
    userId?: number,
  ) {
    try {
      const transaction = await this.transactionRepository.findById(id);
      if (!transaction) {
        throw new NotFoundException('Transaction not found');
      }

      const updatedTransaction = await this.transactionRepository.update(id, {
        status: status,
      });

      if (!updatedTransaction) {
        throw new InternalServerErrorException('Failed to update transaction');
      }

      // Send notifications for status updates
      await this.sendStatusUpdateNotifications(
        updatedTransaction,
        status,
        userId,
      );

      return updatedTransaction;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async completeTransaction(
    userId: number,
    completeTransactionDto: CompleteTransactionDto,
  ) {
    try {
      const transaction = await this.transactionRepository.findById(
        completeTransactionDto.transactionId,
      );
      if (!transaction) throw new NotFoundException('Transaction not found');

      if (transaction.initiatedBy !== userId)
        throw new BadRequestException(
          'Only the transaction receiver can complete the transaction',
        );

      if (transaction.status !== 'accepted')
        throw new BadRequestException(
          'Only transactions with "accepted" status can be completed',
        );

      const updatedTransaction = await this.transactionRepository.update(
        transaction.id,
        { status: 'completed' },
      );

      if (!updatedTransaction) {
        throw new BadRequestException('Failed to update transaction');
      }

      await this.escrowRepository.update(transaction.id, {
        status: 'completed',
      });

      // get recievers default account

      if (transaction.receivedBy && transaction.paymentMethodId) {
        const paymentMethod =
          await this.paymentMethodRepository.getUsersDefautltPaymentMethod(
            transaction.receivedBy,
          );

        if (!paymentMethod) {
          throw new BadRequestException('No payment method found');
        }

        if (paymentMethod.type !== 'mobile_money') {
          throw new BadRequestException('Invalid payment method');
        }

        this.campayService
          .disbursement({
            amount: +transaction.amount,
            phone: paymentMethod?.accountNumber!.replace('+', '').trim(),
            description: transaction.title,
            transactionId: transaction.id,
            paymentMethodId: transaction.paymentMethodId,
            type: 'disbursement',
          })
          .then((res) => {
            this.logger.log('Disbursement initiated', res);
          })
          .catch((err) => {
            this.logger.error('Failed to initiate disbursement', err);
          });
      }

      await this.sendStatusUpdateNotifications(
        updatedTransaction,
        'completed',
        userId,
      );

      return updatedTransaction;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  private async sendStatusUpdateNotifications(
    transaction: Transaction,
    status: string,
    updatedBy?: number,
  ): Promise<void> {
    try {
      const initiator = await this.userRepository.findById(
        transaction.initiatedBy!,
      );
      const receiver = await this.userRepository.findById(
        transaction.receivedBy!,
      );

      if (!initiator || !receiver) return;

      const statusMessages = {
        completed: 'has been completed successfully',
        disputed: 'is under dispute',
        cancelled: 'has been cancelled',
        expired: 'has expired',
        failed: 'has failed',
      };

      const message =
        statusMessages[status as keyof typeof statusMessages] ||
        `status has been updated to ${status}`;

      // Send emails to both parties
      // await this.emailsService.sendTransactionStatusUpdateEmail(
      //   initiator.email,
      //   initiator.name,
      //   transaction.title,
      //   status,
      // );

      // await this.emailsService.sendTransactionStatusUpdateEmail(
      //   receiver.email,
      //   receiver.name,
      //   transaction.title,
      //   status,
      // );

      // Create notifications for both parties
      await this.notificationsService.create({
        title: 'Transaction Status Update',
        message: `Transaction "${transaction.title}" ${message}`,
        userId: initiator.id,
        type: 'personal',
        category: 'transactions',
        priority: 'active',
        metadata: {
          transactionId: transaction.id,
          status,
          updatedBy,
          model: 'transaction',
        },
      });

      await this.notificationsService.create({
        title: 'Transaction Status Update',
        message: `Transaction "${transaction.title}" ${message}`,
        userId: receiver.id,
        type: 'personal',
        category: 'transactions',
        priority: 'active',
        metadata: {
          transactionId: transaction.id,
          status,
          updatedBy,
          model: 'transaction',
        },
      });
      // Send push notifications to both parties
      if (initiator.notificationToken) {
        await this.pushNotificationService.send({
          title: 'Transaction Status Update',
          body: `Transaction "${transaction.title}" ${message}`,
          pushToken: initiator.notificationToken,
          data: {
            transactionId: transaction.id,
          },
        });
      }
      if (receiver.notificationToken) {
        await this.pushNotificationService.send({
          title: 'Transaction Status Update',
          body: `Transaction "${transaction.title}" ${message}`,
          pushToken: receiver.notificationToken,
          data: {
            transactionId: transaction.id,
          },
        });
      }
    } catch (error) {
      console.error('Failed to send status update notifications:', error);
    }
  }

  async deleteTransaction(id: number, userId: number) {
    try {
      const transaction = await this.transactionRepository.findById(id);
      if (!transaction) {
        throw new NotFoundException('Transaction not found');
      }

      if (transaction.initiatedBy !== userId)
        throw new BadRequestException(
          'Only the transaction initiator can delete the transaction',
        );

      if (transaction.status !== 'pending')
        throw new BadRequestException(
          'Only transactions with "pending" status can be deleted',
        );

      const initiator = await this.userRepository.findById(
        transaction.initiatedBy,
      );

      const deleted = await this.transactionRepository.deleteAndReturn(id);

      if (deleted) {
        if (initiator) {
          await this.emailsService.sendTransactionCancelledEmail(
            initiator.email,
            initiator.name,
            transaction.title,
            initiator.name,
          );

          await this.notificationsService.create({
            title: 'Transaction Cancelled',
            message: `Transaction "${transaction.title}" has been cancelled by ${initiator.name}`,
            userId: initiator.id,
            type: 'personal',
            category: 'transactions',
            priority: 'declined',
            metadata: {
              transactionId: transaction.id,
              cancelledBy: userId,
              model: 'transaction',
            },
          });
          if (initiator.notificationToken) {
            await this.pushNotificationService.send({
              title: 'Transaction Cancelled',
              body: `Transaction "${transaction.title}" has been cancelled by ${initiator.name}`,
              pushToken: initiator.notificationToken,
              data: {
                transactionId: transaction.id,
              },
            });
          }
        }

        // disbures ammount in there is payment
        if (transaction.paymentMethodId) {
          const paymentMethod = await this.paymentMethodRepository.findById(
            transaction.paymentMethodId,
          );

          if (!paymentMethod)
            throw new NotFoundException('Payment method not found');

          if (paymentMethod.type === 'mobile_money') {
            if (paymentMethod.accountNumber) {
              this.campayService
                .disbursement({
                  amount: +transaction.amount,
                  phone: paymentMethod.accountNumber.replace('+', '').trim(),
                  description: transaction.title,
                  transactionId: transaction.id,
                  paymentMethodId: paymentMethod.id,
                  type: 'refund',
                  direction: 'from_escrow',
                })
                .then(async (res) => {
                  if (res.res.reference) {
                    await this.transactionRepository.update(transaction.id, {
                      status: 'cancelled',
                    });
                  }
                })
                .catch((err) => {
                  console.log(err); // Handle the error as needed
                });
            }
          }
        }
      }

      return deleted;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async acceptTransaction(
    userId: number,
    acceptTransactionDto: AcceptTransactionDto,
  ) {
    try {
      const transaction = await this.transactionRepository.findById(
        acceptTransactionDto.transactionId,
      );
      if (!transaction) throw new NotFoundException('Transaction not found');

      if (transaction.receivedBy !== userId)
        throw new BadRequestException(
          'Only the transaction receiver can accept the transaction',
        );

      if (transaction.status !== 'pending')
        throw new BadRequestException(
          'Only transactions with "pending" status can be accepted',
        );

      const updatedTransaction = await this.transactionRepository.update(
        transaction.id,
        { status: 'active' },
      );

      if (!updatedTransaction) {
        throw new InternalServerErrorException('Failed to update transaction');
      }

      this.transactionNotification
        .activeTransactionNotification(updatedTransaction.id)
        .catch((err) => {
          this.logger.log(err);
        });

      return updatedTransaction;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getTransactionsByStatus(
    status: TransactionStatus,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction>> {
    try {
      const filterQuery = { ...query, status };
      return await this.transactionRepository.findAll(filterQuery);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getTransactionsByDateRange(
    startDate: string,
    endDate: string,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction>> {
    try {
      const filterQuery = {
        ...query,
        createdFrom: startDate,
        createdTo: endDate,
      };
      return await this.transactionRepository.findAll(filterQuery);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getTransactionsByAmountRange(
    minAmount: number,
    maxAmount: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction>> {
    try {
      const filterQuery = {
        ...query,
        minAmount,
        maxAmount,
      };
      return await this.transactionRepository.findAll(filterQuery);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async searchTransactions(
    searchTerm: string,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction>> {
    try {
      const filterQuery = { ...query, search: searchTerm };
      return await this.transactionRepository.findAll(filterQuery);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getTransactionsByCategory(
    category: string,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction>> {
    try {
      const filterQuery = { ...query, category };
      return await this.transactionRepository.findAll(filterQuery);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getTransactionsByPaymentMethod(
    paymentMethodId: number,
    query: TransactionFilter,
    userId: number,
  ) {
    try {
      const transactions =
        await this.transactionRepository.findByPaymentMethodId(
          paymentMethodId,
          userId,
          query,
        );
      return transactions;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch transactions by payment method',
        error as Error,
      );
    }
  }

  async getRecentTransactions(userId: number) {
    try {
      return await this.transactionRepository.getRecentTransactions(userId);
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch recent transactions',
        error as Error,
      );
    }
  }

  async getStatusCount(userId: number) {
    try {
      return await this.transactionRepository.getStatusCount(userId);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
}
