import { UserRepository } from './../common/repository/user.repository';
import {
  ConflictException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import {
  ProfileRepository,
  PreferenceRepository,
  TransactionRepository,
} from 'src/common/repository';
import { FilesService } from 'src/core/files/files.service';
import { UpdateProfileDto } from './dto/update.dto';
import { Preference } from 'src/common/schemas';

@Injectable()
export class ProfileService {
  constructor(
    private readonly profileRepository: ProfileRepository,
    private readonly fileService: FilesService,
    private readonly userRepository: UserRepository,
    private readonly preferenceRepository: PreferenceRepository,
    private readonly transactionRepository: TransactionRepository,
  ) {}

  async getProfile(userId: number) {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) throw new NotFoundException('User not found');
      const profile =
        await this.profileRepository.findByUserIdWithUserInfo(userId);
      if (!profile) throw new NotFoundException('Profile not found');
      return {
        ...profile,
        stats: await this.transactionRepository.getStatusCount(userId),
      };
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async checkUserProfile(code: string) {
    try {
      const profile = await this.profileRepository.findUserByCode(code);
      if (!profile) throw new NotFoundException('Profile not found');
      if (!profile.user.id) throw new NotFoundException('Profile not found');
      const stats = await this.transactionRepository.getStatusCount(
        profile.user.id,
      );
      return {
        ...profile,
        stats,
      };
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async updateProfile(
    userId: number,
    profileData: UpdateProfileDto,
    image: Express.Multer.File,
  ) {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) throw new NotFoundException('User not found');

      let phoneUpdated = false;
      if (profileData.phone) {
        const existingUser = await this.userRepository.findByPhone(
          profileData.phone,
        );
        if (existingUser) {
          throw new ConflictException('Phone number already exists');
        }
        const updatedUser = await this.userRepository.update(userId, {
          phone: profileData.phone,
          isPhoneVerified: false,
        });
        if (updatedUser) {
          phoneUpdated = true;
        }
      }
      const existingProfile = await this.profileRepository.findByUserId(userId);
      if (!existingProfile) throw new Error('Profile not found');

      if (image) {
        const uploadedFile = await this.fileService.uploadFile(image);
        profileData.avatar = uploadedFile.url;
      }

      const updatedProfile = await this.profileRepository.update(
        existingProfile.id,
        profileData,
      );

      // TODO: Send OTP verification if phone was updated
      if (phoneUpdated) {
        // Add OTP sending logic here
      }

      return updatedProfile;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async updateProfileImage(userId: number, file: Express.Multer.File) {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) throw new NotFoundException('User not found');
      const profile = await this.profileRepository.findByUserId(userId);
      if (!profile) throw new NotFoundException('Profile not found');

      if (profile.avatar) {
        const publicId = this.fileService.extractPublicIdFromUrl(
          profile.avatar,
        );
        if (publicId) await this.fileService.deleteFile(publicId);
      }
      const uploadedFile = await this.fileService.uploadFile(file);
      const updatedProfile = await this.profileRepository.update(profile.id, {
        avatar: uploadedFile.url,
      });
      return updatedProfile;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
  async getPreferences(userId: number) {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) throw new NotFoundException('User not found');
      const preferences = await this.preferenceRepository.findByUserId(userId);
      if (!preferences) throw new NotFoundException('Preferences not found');
      return preferences;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async updatePreferences(
    userId: number,
    preferencesData: Partial<Preference>,
  ) {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) throw new NotFoundException('User not found');
      const preferences = await this.preferenceRepository.update(
        userId,
        preferencesData,
      );
      if (!preferences) throw new NotFoundException('Preferences not found');
      return preferences;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
}
