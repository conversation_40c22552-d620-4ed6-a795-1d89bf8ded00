import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { User } from 'src/common/decorators/user.decorator';
import { PaginationQueryDto } from 'src/common/dto/pagination.query.do';
import { JwtAuthGuard } from 'src/common/guards/jwt.guard';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AddTokenDto } from './dto/add-token.dto';
import { Roles } from 'src/common/decorators/roles.decorator';

@ApiTags('Notifications')
@Controller('notifications')
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Get('user')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get notifications for current user' })
  @ApiResponse({ status: 200, description: 'Returns paginated notifications' })
  findByUserId(@User('id') userId: number, @Query() query: PaginationQueryDto) {
    return this.notificationsService.findByUserId(userId, query);
  }

  @Get('admin')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @Roles('admin', 'super_admin')
  @ApiOperation({ summary: 'Get admin notifications' })
  @ApiResponse({ status: 200, description: 'Returns paginated notifications' })
  adminNotification(@Query() query: PaginationQueryDto) {
    return this.notificationsService.adminNotifications(query);
  }

  @Get('unread-count')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get unread notification count for current user' })
  @ApiResponse({ status: 200, description: 'Returns the count' })
  unreadCount(@User('id') userId: number) {
    return this.notificationsService.unreadCount(userId);
  }

  @Get(':id')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get notification by ID' })
  @ApiResponse({ status: 200, description: 'Returns the notification' })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  findById(@Param('id', ParseIntPipe) id: number) {
    return this.notificationsService.findById(id);
  }

  @Post('push-token')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Add notification token' })
  @ApiResponse({ status: 200, description: 'Token added successfully' })
  addToken(@User('id') userId: number, @Body() dto: AddTokenDto) {
    return this.notificationsService.addPushToken(userId, dto.token);
  }

  @Patch(':id/read')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Mark notification as read' })
  @ApiResponse({ status: 200, description: 'Notification marked as read' })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  markAsRead(@Param('id', ParseIntPipe) id: number) {
    return this.notificationsService.markAsRead(id);
  }

  @Patch('user/read')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Mark all notifications as read' })
  @ApiResponse({ status: 200, description: 'All notifications marked as read' })
  markAsReadAll(@User('id') userId: number) {
    return this.notificationsService.markAsReadAll(userId);
  }

  @Delete(':id')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Delete notification' })
  @ApiResponse({ status: 200, description: 'Notification deleted' })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  delete(@Param('id', ParseIntPipe) id: number) {
    return this.notificationsService.delete(id);
  }
  @Post('test')
  @ApiOperation({ summary: 'Test admin notification' })
  @ApiResponse({ status: 200, description: 'Notification sent' })
  testAdminNotification() {
    return this.notificationsService.testAdminNotification();
  }

  @Delete('clear')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Delete all notifications for current user' })
  @ApiResponse({ status: 200, description: 'All notifications deleted' })
  deleteAll(@User('id') userId: number) {
    return this.notificationsService.deleteAll(userId);
  }
}
