import { Injectable, Inject } from '@nestjs/common';
import { and, count, desc, eq, not, or } from 'drizzle-orm';
import { users, User, NewUser, profiles, Profile } from '../schemas';
import * as schema from '../schemas/users';
import { IUserRepository } from '../interfaces/repository/user-repository.interface';
import { DATABASE_CONNECTION } from 'src/configs';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import {
  PaginatedResponse,
  PaginationMeta,
  PaginationQuery,
} from '../interfaces/paginaton';

@Injectable()
export class UserRepository implements IUserRepository {
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}
  async findAllAdmin(): Promise<{ user: Partial<User>; profile: Profile }[]> {
    const result = await this.db
      .select({
        user: {
          id: users.id,
          name: users.name,
          email: users.email,
          role: users.role,
          referralCode: users.referralCode,
          googleId: users.googleId,
          phone: users.phone,
          isEmailVerified: users.isEmailVerified,
          isPhoneVerified: users.isPhoneVerified,
          isActive: users.isActive,
          isVerified: users.isVerified,
          isDeleted: users.isDeleted,
          isBanned: users.isBanned,

          createdAt: users.createdAt,
        },
        profile: profiles,
      })
      .from(users)
      .where(or(eq(users.role, 'admin'), eq(users.role, 'super_admin')))
      .innerJoin(profiles, eq(users.id, profiles.userId))
      .limit(100);

    return result;
  }
  async bandUser(id: number): Promise<User | null> {
    const result = await this.db
      .update(users)
      .set({
        isBanned: true,
        bannedAt: new Date(),
      })
      .where(eq(users.id, id))
      .returning();

    return result[0] || null;
  }
  async restoreUser(id: number): Promise<User | null> {
    const result = await this.db
      .update(users)
      .set({
        isBanned: false,
        bannedAt: undefined,
      })
      .where(eq(users.id, id))
      .returning();

    return result[0] || null;
  }
  async softDeleteUser(id: number): Promise<User | null> {
    const result = await this.db
      .update(users)
      .set({
        isDeleted: true,
        deletedAt: new Date(),
      })
      .where(eq(users.id, id))
      .returning();

    return result[0] || null;
  }

  async findAll(query: PaginationQuery): Promise<PaginatedResponse<any>> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;

    // Get total count
    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(users)
      .leftJoin(profiles, eq(users.id, profiles.userId))
      .where(
        and(
          eq(users.isDeleted, false),
          not(eq(users.role, 'admin')),
          not(eq(users.role, 'super_admin')),
        ),
      );

    // Get paginated data with relations
    const data = await this.db
      .select({
        id: users.id,
        name: users.name,
        email: users.email,
        role: users.role,
        referralCode: users.referralCode,
        googleId: users.googleId,
        phone: users.phone,
        isEmailVerified: users.isEmailVerified,
        isPhoneVerified: users.isPhoneVerified,
        isActive: users.isActive,
        isVerified: users.isVerified,
        isDeleted: users.isDeleted,
        isBanned: users.isBanned,

        createdAt: users.createdAt,
        profile: {
          id: profiles.id,
          firstName: profiles.firstName,
          lastName: profiles.lastName,
          code: profiles.code,
          avatar: profiles.avatar,
        },
      })
      .from(users)
      .leftJoin(profiles, eq(users.id, profiles.userId))
      .where(eq(users.isDeleted, false))
      .orderBy(desc(users.createdAt))
      .limit(limit)
      .offset(offset);

    const totalPages = Math.ceil(totalItems / limit);

    const meta: PaginationMeta = {
      currentPage: page,
      itemsPerPage: limit,
      totalItems,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };

    return { data, meta };
  }

  async findById(id: number): Promise<User | null> {
    const result = await this.db
      .select()
      .from(users)
      .where(eq(users.id, id))
      .limit(1);

    return result[0] || null;
  }

  async findByEmail(email: string): Promise<User | null> {
    const result = await this.db
      .select()
      .from(users)
      .where(eq(users.email, email))
      .limit(1);

    return result[0] || null;
  }

  async findByPhone(phone: string): Promise<User | null> {
    const result = await this.db
      .select()
      .from(users)
      .where(eq(users.phone, phone))
      .limit(1);

    return result[0] || null;
  }

  async findActiveUsers(): Promise<User[]> {
    return this.db.select().from(users).where(eq(users.isActive, true));
  }

  async create(userData: NewUser): Promise<Partial<User>> {
    const result = await this.db
      .insert(users)
      .values({
        ...userData,
        bannedAt: undefined,
        deletedAt: undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning({
        id: users.id,
        name: users.name,
        email: users.email,
        phone: users.phone,
        role: users.role,
        googleId: users.googleId,
        isActive: users.isActive,
        isVerified: users.isVerified,
        isDeleted: users.isDeleted,
        isBanned: users.isBanned,
        bannedAt: users.bannedAt,
        deletedAt: users.deletedAt,
        verifiedAt: users.verifiedAt,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
      });

    return result[0];
  }

  async update(id: number, userData: Partial<User>): Promise<User | null> {
    const result = await this.db
      .update(users)
      .set({
        ...userData,
        updatedAt: new Date(),
      })
      .where(eq(users.id, id))
      .returning();

    return result[0] || null;
  }

  async deactivateUser(id: number): Promise<User | null> {
    return this.update(id, { isActive: false });
  }

  async delete(id: number): Promise<boolean> {
    const result = await this.db
      .delete(users)
      .where(eq(users.id, id))
      .returning({ id: users.id });

    return result.length > 0;
  }
  async findUserByReferralCode(code: string): Promise<User | null> {
    const result = await this.db
      .select()
      .from(users)
      .where(eq(users.referralCode, code))
      .limit(1);

    return result[0] || null;
  }
}
