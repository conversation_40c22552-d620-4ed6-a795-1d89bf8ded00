/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { HttpService } from '@nestjs/axios';

import { PaymentRepository, UserRepository } from 'src/common/repository';
import { Payment } from 'src/common/schemas';
import {
  CampayTokenResponse,
  CampayTransactionResponse,
} from './response.schema';
import { TransactionNotificationService } from './transaction-notification.service';

@Injectable()
export class CampayService {
  private readonly baseUrl = 'https://demo.campay.net/api';
  private token: string;
  private pamenantToken: string | undefined =
    process.env.CAMPAY_PAMENENT_ACCESS;
  private serviceSecret: string | undefined;
  private tokenExpiry: Date;
  private logger = new Logger(CampayService.name);

  constructor(
    private readonly userRepository: UserRepository,
    private readonly paymentRepository: PaymentRepository,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly transactionNotification: TransactionNotificationService,
  ) {}

  private async getToken(): Promise<string> {
    if (this.token && this.tokenExpiry && new Date() < this.tokenExpiry) {
      return this.token;
    }

    try {
      const response = await firstValueFrom(
        this.httpService.post<CampayTokenResponse>(`${this.baseUrl}/token/`, {
          username: this.configService.get('CAMPAY_USERNAME') as string,
          password: this.configService.get('CAMPAY_PASSWORD') as string,
        }),
      );

      this.token = response.data.token;
      this.tokenExpiry = new Date(Date.now() + 50 * 60 * 1000); // 50 minutes
      return this.token;
    } catch (error: any) {
      throw new HttpException(
        'Failed to get Campay token: ' + error.response?.data?.message,
        HttpStatus.UNAUTHORIZED,
      );
    }
  }

  async collectPayment(data: {
    amount: number;
    phone: string;
    description?: string;
    email: string;
    direction: 'from_escrow' | 'to_escrow' | 'to_reciver';
    transactionId: number;
    paymentMethodId: number;
  }) {
    const user = await this.userRepository.findByEmail(data.email);
    if (!user) throw new HttpException('User not found', HttpStatus.NOT_FOUND);
    const tempRef = `Ref-holdex-${Date.now()}`;
    const paymentId = `PAY-${Date.now()}`;
    let payment: Payment = await this.paymentRepository.create({
      amount: data.amount,
      reference: tempRef,
      status: 'pending',
      type: 'collection',
      direction: data.direction,
      paymentId: paymentId,
      transactionId: data.transactionId,
      paymentMethodId: data.paymentMethodId,
      description: data.description || 'Payment',
      currency: 'XOF',
    });

    try {
      this.logger.debug('transaction initiated');
      const response = await firstValueFrom(
        this.httpService.post<CampayTransactionResponse>(
          `${this.baseUrl}/collect/`,
          {
            amount: data.amount.toString(),
            from: data.phone,
            description: data.description || 'Payment',
            external_reference: paymentId,
          },
          {
            headers: {
              Authorization: `Token ${this.pamenantToken}`,
              'Content-Type': 'application/json',
            },
          },
        ),
      );
      this.logger.log(`Campay response: ${JSON.stringify(response.data)}`);

      if (response.status === 200) {
        payment = await this.paymentRepository.update(payment.id, {
          reference: response.data.reference,
          paymentId: response.data.reference,
          status: 'pending',
        });

        this.transactionNotification
          .initiatePaymentNotification(payment.id)
          .catch(() => {
            this.logger.error('Payment notification failed');
          });
      }

      if (response.status !== 200) {
        payment = await this.paymentRepository.update(payment.id, {
          status: 'failed',
        });
        this.transactionNotification
          .failedPaymentNotification(payment.id)
          .catch((err) =>
            this.logger.error('Failed to send payment notification', err),
          );
      }

      return {
        res: response.data,
        payment,
      };
    } catch (error: any) {
      await this.paymentRepository.update(payment.id, {
        status: 'failed',
      });

      this.transactionNotification
        .failedPaymentNotification(payment.id)
        .catch((err) =>
          this.logger.error('Failed to send payment notification', err),
        );

      throw new HttpException(
        error.response?.data?.message + 'Payment collection failed',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async disbursement(data: {
    amount: number;
    phone: string;
    description?: string;
    transactionId: number;
    paymentMethodId: number;
    type: 'disbursement' | 'refund';
    direction?: 'to_reciver' | 'from_escrow';
  }) {
    const tempRef = `Ref-holdex-${Date.now()}`;
    const paymentId = `PAY-${Date.now()}`;
    let payment: Payment = await this.paymentRepository.create({
      amount: data.amount,
      reference: tempRef,
      type: data.type,
      status: 'pending',
      direction: data.direction || 'to_reciver',
      paymentId: paymentId,
      transactionId: data.transactionId,
      paymentMethodId: data.paymentMethodId,
      description: data.description || 'Transfer',
      currency: 'XOF',
    });

    try {
      this.logger.debug('disbursement initiated');
      const response = await firstValueFrom(
        this.httpService.post<{ reference: string }>(
          `${this.baseUrl}/withdraw/`,
          {
            amount: data.amount.toString(),
            to: data.phone,
            description: data.description || 'Transfer',
            external_reference: paymentId,
          },
          {
            headers: {
              Authorization: `Token ${this.pamenantToken}`,
              'Content-Type': 'application/json',
            },
          },
        ),
      );
      this.logger.log(`Campay response: ${JSON.stringify(response.data)}`);

      if (response.status === 200) {
        payment = await this.paymentRepository.update(payment.id, {
          reference: response.data.reference,
          paymentId: response.data.reference,
          status: 'pending',
        });
        // this.transactionNotification
        //   .initiateDisbursementNotification(payment.id)
        //   .catch((err) =>
        //     this.logger.debug('Disbursement has been initiated', err),
        //   );
      }

      if (response.status !== 200) {
        payment = await this.paymentRepository.update(payment.id, {
          status: 'failed',
        });
        this.transactionNotification
          .failedDisbursementNotification(payment.id)
          .catch((err) =>
            this.logger.error('Failed to send payment notification', err),
          );
      }

      return {
        res: response.data,
        payment,
      };
    } catch (error: any) {
      await this.paymentRepository.update(payment.id, {
        status: 'failed',
      });

      this.transactionNotification
        .failedDisbursementNotification(payment.id)
        .catch((err) =>
          this.logger.error('Failed to send payment notification', err),
        );
      throw new HttpException(
        error.response?.data?.message + 'Disbursement failed',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async getTransactionStatus(reference: string) {
    try {
      const response = await firstValueFrom(
        this.httpService.get<CampayTransactionResponse>(
          `${this.baseUrl}/transaction/${reference}/`,
          {
            headers: {
              Authorization: `Token ${this.pamenantToken}`,
            },
          },
        ),
      );

      return response.data;
    } catch (error: any) {
      throw new HttpException(
        'Failed to get transaction status' + error.response?.data?.message,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  // async testEndpoint({ phone }: { phone: string }) {
  //   try {
  //     const response = await firstValueFrom(
  //       this.httpService.post(
  //         `${this.baseUrl}/collect/`,
  //         {
  //           amount: '100',
  //           from: phone,
  //           description: 'Test',
  //           external_reference: `TEST-${Date.now()}`,
  //         },
  //         {
  //           headers: {
  //             Authorization: `Token ${this.pamenantToken}`,
  //           },
  //         },
  //       ),
  //     );

  //     return {
  //       success: true,
  //       data: response.data,
  //       message: 'Campay API is reachable',
  //     };
  //   } catch (error: any) {
  //     throw new HttpException(
  //       'Failed to reach Campay API: ' + error.response?.data?.message,
  //       HttpStatus.SERVICE_UNAVAILABLE,
  //     );
  //   }
  // }
}
