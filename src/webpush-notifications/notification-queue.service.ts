/* eslint-disable @typescript-eslint/no-misused-promises */

/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import {
  Injectable,
  Logger,
  OnModuleDestroy,
  OnModuleInit,
} from '@nestjs/common';
import Queue from 'better-queue';
import SqliteStore from 'better-queue-sqlite';
import { join } from 'path';
import { cwd } from 'process';
import { QueuedNotification } from 'src/common/interfaces/queue.interface';

// We no longer need the RawTaskRow interface as we use the queue.get method

@Injectable()
export class NotificationQueueService implements OnModuleInit, OnModuleDestroy {
  private queue: Queue<QueuedNotification>;
  private store: SqliteStore; // We still initialize the store
  private cleanupInterval: NodeJS.Timeout;

  private logger = new Logger(NotificationQueueService.name);

  onModuleInit() {
    const dbPath = join(cwd(), 'data', 'notifications.db');

    this.store = new SqliteStore({
      path: dbPath,
      table: 'notifications_queue',
      // 'id' must be the property used in the task object passed to queue.push
      idProperty: 'id',
    });

    this.queue = new Queue(
      (task: QueuedNotification, done) => {
        this.logger.log(
          `Processing notification for userId ${task.userId}: ${task.notification.title}`,
        );
        done(null, task);
      },
      {
        store: this.store,
        concurrent: 1,
        autoResume: true,
      },
    );

    this.logger.log('✅ Notification queue initialized using pure SQLite');

    // This interval will now call a function that respects the public API
    this.cleanupInterval = setInterval(
      () => this.cleanupExpired(),
      1000 * 60 * 60,
    );
  }

  // ... enqueue method remains the same ...
  async enqueue(
    userId: number,
    notification: {
      title: string;
      message: string;
      type: 'info' | 'warning' | 'error' | 'success';
      data?: any;
    },
    expiresInDays = 7,
  ) {
    const taskId = `notif_${Date.now()}_${Math.random().toString(36).slice(2)}`;

    const task: QueuedNotification & { id: string } = {
      id: taskId,
      userId,
      notification: {
        id: taskId,
        ...notification,
        timestamp: new Date().toISOString(),
        read: false,
      },
      expiresAt: Date.now() + expiresInDays * 24 * 60 * 60 * 1000,
    };

    return new Promise<void>((resolve, reject) => {
      this.queue.push(task, (err: Error) => {
        if (err) return reject(err);
        this.logger.log('✅ Notification saved to queue');
        resolve();
      });
    });
  }

  /**
   * CORRECTED: Use queue.get() instead of the non-existent store.getPendingTasks()
   */
  async getPendingNotifications(
    userId: number,
  ): Promise<QueuedNotification['notification'][]> {
    // queue.get(criteria) without criteria fetches all items from the store
    const tasks = await new Promise<QueuedNotification[]>((resolve, reject) => {
      // The callback receives an array of the original task objects
      this.queue.get(null as any, (err, items) => {
        if (err) return reject(err);
        resolve(items as QueuedNotification[]);
      });
    });

    // Filter and sort the results as before
    return tasks
      .filter((t) => t.userId === userId && t.expiresAt > Date.now())
      .map((t) => t.notification)
      .sort(
        (a, b) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
      );
  }

  /**
   * This still cannot be implemented with the public API for immediate deletion
   * outside the worker process. The warning remains.
   */
  markAsDelivered(_userId: number, notificationIds: string[]) {
    if (notificationIds.length === 0) return;

    this.logger.warn(
      `🗑 Cannot remove ${notificationIds.length} notifications from queue using public API. 
      Tasks must be processed by the queue worker or expire naturally.`,
    );
  }

  /**
   * The cleanup function now also uses queue.get()
   */
  private async cleanupExpired() {
    this.logger.log('Running expired notification cleanup...');

    const tasks = await new Promise<QueuedNotification[]>((resolve, reject) => {
      this.queue.(null as any, (err, items) => {
        if (err) return reject(err);
        resolve(items as QueuedNotification[]);
      });
    });

    const expiredIds: string[] = tasks
      .filter((t) => t.expiresAt <= Date.now())
      .map((t) => t.notification.id);

    if (expiredIds.length === 0) {
      return;
    }

    this.logger.log(
      `🧹 Identified ${expiredIds.length} expired notifications, but cannot remove them without direct SQL access.`,
    );
  }

  onModuleDestroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    return new Promise<void>((resolve) =>
      this.queue.destroy(() => {
        // The SqliteStore does not have a public .close() method.
        this.logger.log('⛔ Queue gracefully stopped');
        resolve();
      }),
    );
  }
}
