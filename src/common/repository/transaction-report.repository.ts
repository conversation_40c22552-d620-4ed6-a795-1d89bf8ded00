import { Injectable, Inject } from '@nestjs/common';
import { count, desc, eq } from 'drizzle-orm';
import { reports, Report, transactions, users } from '../schemas';
import * as schema from '../schemas';
import { ITransactionReportRepository } from '../interfaces/repository/transaction-report.interface';
import { DATABASE_CONNECTION } from 'src/configs';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { PaginatedResponse, PaginationQuery } from '../interfaces/paginaton';

@Injectable()
export class TransactionReportRepository
  implements ITransactionReportRepository
{
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}
  async findById(id: number): Promise<Report | null> {
    const result = await this.db
      .select()
      .from(reports)
      .where(eq(reports.id, id))
      .limit(1);

    return result[0] || null;
  }

  async findByUserId(
    userId: number,
    query: PaginationQuery,
  ): Promise<PaginatedResponse<Report> | null> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;

    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(reports)
      .where(eq(reports.reporterId, userId));

    const data = await this.db
      .select()
      .from(reports)
      .where(eq(reports.reporterId, userId))
      .limit(limit)
      .offset(offset)
      .orderBy(desc(reports.createdAt));

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async findAll(query: PaginationQuery): Promise<PaginatedResponse<{
    report: Report;
    transaction: any;
    reporter: any;
  }> | null> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;

    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(reports);

    const data = await this.db
      .select({
        report: reports,
        transaction: transactions,
        reporter: users,
      })
      .from(reports)
      .leftJoin(transactions, eq(reports.transactionId, transactions.id))
      .leftJoin(users, eq(reports.reporterId, users.id))
      .limit(limit)
      .offset(offset)
      .orderBy(desc(reports.createdAt));

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async findByTransactionId(transactionId: number): Promise<Report[]> {
    return await this.db
      .select()
      .from(reports)
      .where(eq(reports.transactionId, transactionId))
      .orderBy(desc(reports.createdAt));
  }

  async create(reportData: schema.NewReport): Promise<Report> {
    const [report] = await this.db
      .insert(reports)
      .values(reportData)
      .returning();
    return report;
  }

  async update(
    id: number,
    reportData: Partial<Report>,
  ): Promise<Report | null> {
    const [report] = await this.db
      .update(reports)
      .set(reportData)
      .where(eq(reports.id, id))
      .returning();
    return report || null;
  }

  async delete(id: number): Promise<boolean> {
    const result = await this.db
      .delete(reports)
      .where(eq(reports.id, id))
      .returning();
    return result.length > 0;
  }
}
