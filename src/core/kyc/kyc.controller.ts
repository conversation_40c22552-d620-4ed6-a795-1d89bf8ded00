/* eslint-disable @typescript-eslint/no-unsafe-return */
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Query,
  Post,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { KycService } from './kyc.service';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { CreateKycDto } from './dto/create.dto';
import { JwtAuthGuard } from 'src/common/guards/jwt.guard';
import { User } from 'src/common/decorators/user.decorator';
import { PaginationQueryDto } from 'src/common/dto/pagination.query.do';
import { RolesGuard } from 'src/common/guards/roles.guard';
import { Roles } from 'src/common/decorators/roles.decorator';
import { RejectKycDto } from './dto/reject.dot';

@ApiTags('KYC')
@ApiBearerAuth()
@Controller('kyc')
export class KycController {
  constructor(private readonly kycService: KycService) {}

  @Post('create/:userId')
  @ApiOperation({ summary: 'Create new KYC record' })
  @ApiResponse({ status: 201, description: 'KYC record created successfully' })
  @ApiResponse({
    status: 400,
    description: 'Bad request or KYC already exists',
  })
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'frontId', maxCount: 1 },
      { name: 'backId', maxCount: 1 },
      { name: 'selfie', maxCount: 1 },
    ]),
  )
  async createKyc(
    @Body() dto: CreateKycDto,
    @UploadedFiles()
    files: {
      frontId?: Express.Multer.File[];
      backId?: Express.Multer.File[];
      selfie?: Express.Multer.File[];
    },
    @Param('userId', ParseIntPipe) userId: number,
  ) {
    return this.kycService.create(dto, userId, files);
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'super_admin')
  @ApiOperation({ summary: 'Get all KYC records' })
  @ApiResponse({ status: 200, description: 'Returns list of KYC records' })
  async getAllKyc(@Query() query: PaginationQueryDto) {
    return this.kycService.getAll(query);
  }

  @Get('user')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get KYC record for current user' })
  @ApiResponse({ status: 200, description: 'Returns user KYC record' })
  @ApiResponse({ status: 404, description: 'KYC not found' })
  async getUserKyc(@User('id') userId: number) {
    return this.kycService.findByUserId(userId);
  }
  @Get(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'super_admin')
  @ApiOperation({ summary: 'Get KYC record by ID' })
  @ApiResponse({ status: 200, description: 'Returns KYC record' })
  @ApiResponse({ status: 404, description: 'KYC not found' })
  async getKycById(@Param('id', ParseIntPipe) id: number) {
    return this.kycService.getKycById(id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete KYC record' })
  @ApiResponse({ status: 200, description: 'KYC record deleted successfully' })
  @ApiResponse({ status: 404, description: 'KYC not found' })
  async deleteKyc(@Param('id', ParseIntPipe) id: number) {
    return this.kycService.delete(id);
  }
  @Post(':id/approve')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'super_admin')
  @ApiOperation({ summary: 'Approve KYC record' })
  @ApiResponse({ status: 200, description: 'KYC record approved successfully' })
  @ApiResponse({ status: 404, description: 'KYC not found' })
  async approveKyc(@Param('id', ParseIntPipe) id: number) {
    return this.kycService.approveKyc(id);
  }

  @Post(':id/reject')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'super_admin')
  @ApiOperation({ summary: 'Reject KYC record' })
  @ApiResponse({ status: 200, description: 'KYC record rejected successfully' })
  @ApiResponse({ status: 404, description: 'KYC not found' })
  async rejectKyc(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: RejectKycDto,
  ) {
    return this.kycService.rejectKyc(id, dto.message);
  }
}
