import { Injectable, Inject } from '@nestjs/common';
import { eq, desc, count, and, like, or, asc, SQL } from 'drizzle-orm';
import {
  transactions,
  Transaction,
  NewTransaction,
  escrow,
  files,
  users,
  User,
  Escrow,
  File,
  paymentMethods,
  PaymentMethod,
  profiles,
  Profile,
  payments,
  Payment,
} from '../schemas';
import * as schema from '../schemas/transaction';
import { ITransactionRepository } from '../interfaces';
import { DATABASE_CONNECTION } from 'src/configs';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { PaginatedResponse, TransactionFilter } from '../interfaces';
import { alias } from 'drizzle-orm/pg-core';
const ALL_STATUSES = [
  'completed',
  'accepted',
  'disputed',
  'cancelled',
  'expired',
  'declined',
  'pending',
  'failed',
  'active',
] as const;
@Injectable()
export class TransactionRepository implements ITransactionRepository {
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}
  async findByTransactionId(transactionId: string): Promise<{
    transaction: Transaction;
    initiatedByUser: User & { profile: Profile | null };
    receivedByUser: User & { profile: Profile | null };
    files: File[];
    escrow: Escrow;
    paymentMethod: PaymentMethod;
    payments: Payment[];
  } | null> {
    const initiatedByUser = alias(users, 'initiatedByUser');
    const receivedByUser = alias(users, 'receivedByUser');
    const initiatedByProfile = alias(profiles, 'initiatedByProfile');
    const receivedByProfile = alias(profiles, 'receivedByProfile');
    const paymentMethodAlias = alias(paymentMethods, 'paymentMethod');

    // First get the transaction with users, profiles and escrow
    const transactionResult = await this.db
      .select({
        transaction: transactions,
        initiatedByUser: initiatedByUser,
        receivedByUser: receivedByUser,
        initiatedByProfile: initiatedByProfile,
        receivedByProfile: receivedByProfile,
        escrow: escrow,
        paymentMethod: paymentMethodAlias,
      })
      .from(transactions)
      .where(eq(transactions.transactionId, transactionId))
      .leftJoin(
        initiatedByUser,
        eq(transactions.initiatedBy, initiatedByUser.id),
      )
      .leftJoin(receivedByUser, eq(transactions.receivedBy, receivedByUser.id))
      .leftJoin(
        initiatedByProfile,
        eq(initiatedByUser.id, initiatedByProfile.userId),
      )
      .leftJoin(
        receivedByProfile,
        eq(receivedByUser.id, receivedByProfile.userId),
      )
      .leftJoin(escrow, eq(transactions.id, escrow.transactionId))
      .leftJoin(
        paymentMethodAlias,
        eq(transactions.paymentMethodId, paymentMethodAlias.id),
      )
      .limit(1);

    if (!transactionResult[0]) {
      return null;
    }

    // Get all files for this transaction
    const filesResult = await this.db
      .select()
      .from(files)
      .where(eq(files.transactionId, transactionResult[0].transaction.id));

    const result = transactionResult[0];
    const paymentsResult = await this.db
      .select()
      .from(payments)
      .where(eq(payments.transactionId, transactionResult[0].transaction.id));

    return {
      transaction: result.transaction,
      initiatedByUser: {
        ...result.initiatedByUser!,
        profile: result.initiatedByProfile,
      },
      receivedByUser: {
        ...result.receivedByUser!,
        profile: result.receivedByProfile,
      },
      files: filesResult,
      escrow: result.escrow!,
      paymentMethod: result.paymentMethod!,
      payments: paymentsResult,
    };
  }
  async getRecentTransactions(userId: number): Promise<
    {
      transaction: Transaction;
      initiatedByUser: User;
      receivedByUser: User;
      files: File[];
      escrow: Escrow;
    }[]
  > {
    const initiatedByUser = alias(users, 'initiatedByUser');
    const receivedByUser = alias(users, 'receivedByUser');

    // Get recent transactions where user is either initiator or receiver
    const transactionResults = await this.db
      .select({
        transaction: transactions,
        initiatedByUser: initiatedByUser,
        receivedByUser: receivedByUser,
        escrow: escrow,
      })
      .from(transactions)
      .where(
        or(
          eq(transactions.initiatedBy, userId),
          eq(transactions.receivedBy, userId),
        ),
      )
      .leftJoin(
        initiatedByUser,
        eq(transactions.initiatedBy, initiatedByUser.id),
      )
      .leftJoin(receivedByUser, eq(transactions.receivedBy, receivedByUser.id))
      .leftJoin(escrow, eq(transactions.id, escrow.transactionId))
      .orderBy(desc(transactions.createdAt))
      .limit(10);

    // Get files for each transaction
    const results = await Promise.all(
      transactionResults.map(async (result) => {
        const filesResult = await this.db
          .select()
          .from(files)
          .where(eq(files.transactionId, result.transaction.id));

        return {
          transaction: result.transaction,
          initiatedByUser: result.initiatedByUser!,
          receivedByUser: result.receivedByUser!,
          files: filesResult,
          escrow: result.escrow!,
        };
      }),
    );

    return results;
  }

  async findAll(query: TransactionFilter): Promise<PaginatedResponse<any>> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;

    const initiatedByUser = alias(users, 'initiatedByUser');
    const receivedByUser = alias(users, 'receivedByUser');

    // Build where conditions
    const whereConditions = this.buildWhereConditions(query);

    // Count total items with filters
    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(transactions)
      .where(whereConditions);

    // Build order by clause
    const orderBy = this.buildOrderBy(query);

    const data = await this.db
      .select({
        transaction: transactions,
        escrow: escrow,
        files: files,
        initiatedByUser: initiatedByUser,
        receivedByUser: receivedByUser,
      })
      .from(transactions)
      .leftJoin(
        initiatedByUser,
        eq(transactions.initiatedBy, initiatedByUser.id),
      )
      .leftJoin(receivedByUser, eq(transactions.receivedBy, receivedByUser.id))
      .leftJoin(escrow, eq(transactions.id, escrow.transactionId))
      .leftJoin(files, eq(transactions.id, files.transactionId))
      .where(whereConditions)
      .limit(limit)
      .offset(offset)
      .orderBy(orderBy);

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async create(transactionData: NewTransaction): Promise<Transaction> {
    const result = await this.db
      .insert(transactions)
      .values({
        ...transactionData,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return result[0];
  }

  async update(
    id: number,
    transactionData: Partial<Transaction>,
  ): Promise<Transaction | null> {
    const result = await this.db
      .update(transactions)
      .set({
        ...transactionData,
        updatedAt: new Date(),
      })
      .where(eq(transactions.id, id))
      .returning();

    return result[0] || null;
  }

  async delete(id: number): Promise<boolean> {
    const result = await this.db
      .delete(transactions)
      .where(eq(transactions.id, id))
      .returning();

    return result.length > 0;
  }

  async deleteAndReturn(id: number): Promise<Transaction | null> {
    const result = await this.db
      .delete(transactions)
      .where(eq(transactions.id, id))
      .returning();

    return result[0] || null;
  }

  async findById(id: number): Promise<Transaction | null> {
    const result = await this.db
      .select()
      .from(transactions)
      .where(eq(transactions.id, id))
      .limit(1);

    return result[0] || null;
  }

  async findByIdWithUsers(id: number): Promise<{
    transaction: Transaction;
    initiatedByUser: User & { profile: Profile | null };
    receivedByUser: User & { profile: Profile | null };
    files: File[];
    escrow: Escrow;
    paymentMethod: PaymentMethod;
    payments: Payment[];
  } | null> {
    const initiatedByUser = alias(users, 'initiatedByUser');
    const receivedByUser = alias(users, 'receivedByUser');
    const initiatedByProfile = alias(profiles, 'initiatedByProfile');
    const receivedByProfile = alias(profiles, 'receivedByProfile');
    const paymentMethodAlias = alias(paymentMethods, 'paymentMethod');

    // First get the transaction with users, profiles and escrow
    const transactionResult = await this.db
      .select({
        transaction: transactions,
        initiatedByUser: initiatedByUser,
        receivedByUser: receivedByUser,
        initiatedByProfile: initiatedByProfile,
        receivedByProfile: receivedByProfile,
        escrow: escrow,
        paymentMethod: paymentMethodAlias,
      })
      .from(transactions)
      .where(eq(transactions.id, id))
      .leftJoin(
        initiatedByUser,
        eq(transactions.initiatedBy, initiatedByUser.id),
      )
      .leftJoin(receivedByUser, eq(transactions.receivedBy, receivedByUser.id))
      .leftJoin(
        initiatedByProfile,
        eq(initiatedByUser.id, initiatedByProfile.userId),
      )
      .leftJoin(
        receivedByProfile,
        eq(receivedByUser.id, receivedByProfile.userId),
      )
      .leftJoin(escrow, eq(transactions.id, escrow.transactionId))
      .leftJoin(
        paymentMethodAlias,
        eq(transactions.paymentMethodId, paymentMethodAlias.id),
      )
      .limit(1);

    if (!transactionResult[0]) {
      return null;
    }

    // Get all files for this transaction
    const filesResult = await this.db
      .select()
      .from(files)
      .where(eq(files.transactionId, id));

    const result = transactionResult[0];
    const paymentsResult = await this.db
      .select()
      .from(payments)
      .where(eq(payments.transactionId, id));

    return {
      transaction: result.transaction,
      initiatedByUser: {
        ...result.initiatedByUser!,
        profile: result.initiatedByProfile,
      },
      receivedByUser: {
        ...result.receivedByUser!,
        profile: result.receivedByProfile,
      },
      files: filesResult,
      escrow: result.escrow!,
      paymentMethod: result.paymentMethod!,
      payments: paymentsResult,
    };
  }

  async findByUserId(
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<any> | null> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;

    const baseConditions = this.buildWhereConditions(query);
    const userCondition = or(
      eq(transactions.initiatedBy, userId),
      eq(transactions.receivedBy, userId),
    );
    const whereConditions = baseConditions
      ? and(userCondition, baseConditions)
      : userCondition;

    const initiatedByUser = alias(users, 'initiatedByUser');
    const receivedByUser = alias(users, 'receivedByUser');

    // Count total items - no joins needed for counting
    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(transactions)
      .where(whereConditions);

    const orderBy = this.buildOrderBy(query);

    // Get transactions without files join to avoid duplicates
    const transactionResults = await this.db
      .select({
        transaction: transactions,
        escrow: escrow,
        initiatedByUser: initiatedByUser,
        receivedByUser: receivedByUser,
      })
      .from(transactions)
      .where(whereConditions)
      .leftJoin(
        initiatedByUser,
        eq(transactions.initiatedBy, initiatedByUser.id),
      )
      .leftJoin(receivedByUser, eq(transactions.receivedBy, receivedByUser.id))
      .leftJoin(escrow, eq(transactions.id, escrow.transactionId))
      .limit(limit)
      .offset(offset)
      .orderBy(orderBy);

    // Fetch files separately for each transaction
    const result = await Promise.all(
      transactionResults.map(async (row) => {
        const filesResult = await this.db
          .select()
          .from(files)
          .where(eq(files.transactionId, row.transaction.id));

        return {
          transaction: row.transaction,
          escrow: row.escrow,
          files: filesResult,
          initiatedByUser: row.initiatedByUser,
          receivedByUser: row.receivedByUser,
        };
      }),
    );

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data: result,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async findByReceivedBy(
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<any> | null> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;

    const baseConditions = this.buildWhereConditions(query);
    const userCondition = eq(transactions.receivedBy, userId);
    const whereConditions = baseConditions
      ? and(userCondition, baseConditions)
      : userCondition;

    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(transactions)
      .where(whereConditions);

    const orderBy = this.buildOrderBy(query);

    const initiatedByUser = alias(users, 'initiatedByUser');
    const receivedByUser = alias(users, 'receivedByUser');

    const result = await this.db
      .select({
        transaction: transactions,
        escrow: escrow,
        files: files,
        initiatedByUser: initiatedByUser,
        receivedByUser: receivedByUser,
      })
      .from(transactions)
      .where(whereConditions)
      .leftJoin(escrow, eq(transactions.id, escrow.transactionId))
      .leftJoin(files, eq(transactions.id, files.transactionId))
      .leftJoin(
        initiatedByUser,
        eq(transactions.initiatedBy, initiatedByUser.id),
      )
      .leftJoin(receivedByUser, eq(transactions.receivedBy, receivedByUser.id))
      .limit(limit)
      .offset(offset)
      .orderBy(orderBy);

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data: result,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async findByEscrowId(escrowId: number): Promise<Transaction | null> {
    const result = await this.db
      .select()
      .from(transactions)
      .where(eq(transactions.id, escrowId))
      .limit(1);

    return result[0] || null;
  }

  async findByPaymentMethodId(
    paymentMethodId: number,
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<any> | null> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;
    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(transactions);
    const initiatedByUser = alias(users, 'initiatedByUser');
    const receivedByUser = alias(users, 'receivedByUser');
    const result = await this.db
      .select({
        transaction: transactions,
        escrow: escrow,
        files: files,
        initiatedByUser: initiatedByUser,
        receivedByUser: receivedByUser,
      })
      .from(transactions)
      .where(
        and(
          eq(transactions.paymentMethodId, paymentMethodId),
          eq(transactions.initiatedBy, userId),
        ),
      )
      .leftJoin(escrow, eq(transactions.id, escrow.transactionId))
      .leftJoin(files, eq(transactions.id, files.transactionId))
      .leftJoin(
        initiatedByUser,
        eq(transactions.initiatedBy, initiatedByUser.id),
      )
      .leftJoin(receivedByUser, eq(transactions.receivedBy, receivedByUser.id))
      .limit(limit)
      .offset(offset)
      .orderBy(desc(transactions.createdAt));

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data: result,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  private buildWhereConditions(
    query: TransactionFilter,
  ): SQL<unknown> | undefined {
    const conditions: SQL<unknown>[] = [];

    if (query.status) conditions.push(eq(transactions.status, query.status));

    if (query.search) {
      conditions.push(
        or(
          like(transactions.title, `%${query.search}%`),
          like(transactions.description, `%${query.search}%`),
        )!,
      );
    }

    return conditions.length > 0 ? and(...conditions) : undefined;
  }

  private buildOrderBy(query: TransactionFilter): SQL<unknown> {
    const sortBy = query.sortBy || 'createdAt';
    const sortOrder = query.sortOrder || 'desc';

    const column = {
      createdAt: transactions.createdAt,
      updatedAt: transactions.updatedAt,
      amount: transactions.amount,
      deadLine: transactions.deadLine,
      title: transactions.title,
    }[sortBy];

    return sortOrder === 'asc' ? asc(column) : desc(column);
  }
  async getStatusCount(userId: number): Promise<Record<string, number>> {
    const total = await this.db
      .select({ count: count() })
      .from(transactions)
      .where(
        or(
          eq(transactions.initiatedBy, userId),
          eq(transactions.receivedBy, userId),
        ),
      );
    const result = await this.db
      .select({
        status: transactions.status,
        count: count(),
      })
      .from(transactions)
      .where(
        or(
          eq(transactions.initiatedBy, userId),
          eq(transactions.receivedBy, userId),
        ),
      )
      .groupBy(transactions.status);

    const statusCounts: Record<string, number> = ALL_STATUSES.reduce(
      (acc, status) => {
        acc[status] = 0;
        return acc;
      },
      {} as Record<string, number>,
    );

    // Update with actual counts from database
    result.forEach((item) => {
      statusCounts[item.status] = item.count;
    });

    return { total: total[0].count, ...statusCounts };
  }
}
