import {
  pgTable,
  serial,
  varchar,
  timestamp,
  boolean,
  pgEnum,
  json,
} from 'drizzle-orm/pg-core';
import { integer } from 'drizzle-orm/pg-core';
import { users } from './users';
import { relations } from 'drizzle-orm';
export const notificationCategoryEnum = pgEnum('category', [
  'transactions',
  'security_alerts',
  'reminders',
  'general_updates',
]);

export const notificationTypeEnum = pgEnum('type', [
  'personal',
  'broadcast',
  'admin',
]);

export const notificationPriorityEnum = pgEnum('priority', [
  'pending',
  'warning',
  'info',
  'active',
  'completed',
  'declined',
  'error',
  'action_required',
]);
export const notifications = pgTable('notifications', {
  id: serial('id').primaryKey(),
  title: varchar('title', { length: 255 }).notNull(),
  type: notificationTypeEnum('type').notNull(),
  message: varchar('message', { length: 255 }).notNull(),
  userId: integer('user_id').references(() => users.id, {
    onDelete: 'cascade',
  }),
  actionRequired: boolean('action_required').default(false),
  metadata: json('metadata'),
  priority: notificationPriorityEnum('priority').default('info'),
  category: notificationCategoryEnum('category'),
  isRead: boolean('is_read').default(false),
  isArchived: boolean('is_archived').default(false),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export type Notification = typeof notifications.$inferSelect;
export type NewNotification = typeof notifications.$inferInsert;

export const notificationRelations = relations(notifications, ({ one }) => ({
  user: one(users, { fields: [notifications.userId], references: [users.id] }),
}));
