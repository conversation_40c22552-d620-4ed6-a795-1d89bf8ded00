import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { NotificationRepository, UserRepository } from 'src/common/repository';
import { NewNotification, Notification } from 'src/common/schemas';
import { PaginationQuery, PaginatedResponse } from 'src/common/interfaces';
import { PushNotificationService } from './pushnotification.service';
import { AdminNotification } from 'src/webpush-notifications/admin-notifiction.gateway';

@Injectable()
export class NotificationsService {
  constructor(
    private readonly notificationRepository: NotificationRepository,
    private readonly userRepository: UserRepository,
    private readonly pushNotification: PushNotificationService,
    private readonly webPush: AdminNotification,
  ) {}

  async addPushToken(userId: number, token: string) {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) throw new NotFoundException('User not found');
      await this.userRepository.update(userId, { notificationToken: token });
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async testAdminNotification() {
    return this.webPush.sendNotificationToAdmins({
      title: 'Test',
      message: 'Test',
      type: 'info',
      data: {
        test: 'test',
      },
    });
  }

  async create(notificationData: NewNotification): Promise<Notification> {
    try {
      // If userId exists in payload, find user and send push notification
      if (notificationData.userId) {
        const user = await this.userRepository.findById(
          notificationData.userId,
        );
        if (user && user.notificationToken) {
          await this.pushNotification.send({
            title: notificationData.title,
            body: notificationData.message,
            pushToken: user.notificationToken,
            data: notificationData.metadata as Record<string, any>,
          });
        }
      }

      return await this.notificationRepository.create(notificationData);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async findById(id: number): Promise<Notification | null> {
    try {
      const notification = await this.notificationRepository.findById(id);
      if (!notification) throw new NotFoundException('Notification not found');
      return notification;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async findByUserId(
    userId: number,
    query: PaginationQuery,
  ): Promise<PaginatedResponse<Notification> | null> {
    console.log(query);

    try {
      return await this.notificationRepository.findByUserId(userId, query);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async markAsRead(id: number): Promise<Notification | null> {
    try {
      const notification = await this.notificationRepository.findById(id);
      if (!notification) throw new NotFoundException('Notification not found');
      return await this.notificationRepository.markAsRead(id);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async markAsReadAll(userId: number): Promise<boolean> {
    try {
      return await this.notificationRepository.markAsReadAll(userId);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async delete(id: number): Promise<boolean> {
    try {
      const notification = await this.notificationRepository.findById(id);
      if (!notification) throw new NotFoundException('Notification not found');
      return await this.notificationRepository.delete(id);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async deleteAll(userId: number): Promise<boolean> {
    try {
      return await this.notificationRepository.deleteAll(userId);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
  async unreadCount(userId: number): Promise<number> {
    try {
      return await this.notificationRepository.unreadCount(userId);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async adminNotifications(
    query: PaginationQuery,
  ): Promise<PaginatedResponse<Notification> | null> {
    try {
      return await this.notificationRepository.adminNotification(query);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
}
