import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { CreateMobileMoneyAccountDto } from './dto/create-momo-account.dto';
import {
  PaymentMethodRepository,
  UserRepository,
  TokenRepository,
} from 'src/common/repository';
import { EmailsService } from 'src/core/emails/emails.service';
import { UpdateAccountDto } from './dto/update-account.dto';
import { VerifyAccountDto } from './dto/verify-account.dto';

@Injectable()
export class AccountsService {
  constructor(
    private readonly paymentMethodRepository: PaymentMethodRepository,
    private readonly userRepository: UserRepository,
    private readonly emailService: EmailsService,
    private readonly tokenRepository: TokenRepository,
  ) {}
  async linkMtnAccount(
    userId: number,
    createAccountDto: CreateMobileMoneyAccountDto,
  ) {
    const user = await this.userRepository.findById(userId);
    if (!user) throw new NotFoundException('User not found');

    const existingAccount =
      await this.paymentMethodRepository.findByAccountNumber(
        createAccountDto.accountNumber,
      );

    if (existingAccount) {
      throw new BadRequestException('Account already exists');
    }

    const paymentMethod =
      await this.paymentMethodRepository.createMtnPaymentMethod(userId, {
        type: 'mobile_money',
        accountName: createAccountDto.accountName,
        countryCode: createAccountDto.countryCode,
        accountNumber: createAccountDto.accountNumber,
        provider: createAccountDto.provider,
        name: 'MTN Mobile Money',
      });

    await this.emailService.sendCustomEmail(
      user.email,
      user.name,
      'Mobile Money Account Verification Required',
      `Your mobile money account has been linked to your account. Please verify your account by calling the number below. <br> <strong>Phone Number: ${createAccountDto.accountNumber}</strong>`,
    );

    return paymentMethod;
  }

  async linkOrangeAccount(
    userId: number,
    createAccountDto: CreateMobileMoneyAccountDto,
  ) {
    const user = await this.userRepository.findById(userId);
    if (!user) throw new NotFoundException('User not found');
    const existingAccount =
      await this.paymentMethodRepository.findByAccountNumber(
        createAccountDto.accountNumber,
      );
    if (existingAccount) {
      throw new BadRequestException('Account already exists');
    }

    const paymentMethod =
      await this.paymentMethodRepository.createOrangePaymentMethod(userId, {
        type: 'mobile_money',
        accountName: createAccountDto.accountName,
        countryCode: createAccountDto.countryCode,
        accountNumber: createAccountDto.accountNumber,
        provider: createAccountDto.provider,
        name: 'Orange Money',
      });
    await this.emailService.sendCustomEmail(
      user.email,
      user.name,
      'Mobile Money Account Verification Required',
      `Your mobile money account has been linked to your account. Please verify your account by calling the number below. <br> <strong>Phone Number: ${createAccountDto.accountNumber}</strong>`,
    );

    return paymentMethod;
  }

  async findAll(userId: number) {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) throw new NotFoundException('User not found');
      return this.paymentMethodRepository.getAllUserPaymentMethods(userId);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async findOne(id: number) {
    try {
      const paymentMethod =
        await this.paymentMethodRepository.getPaymentMethodById(id);
      if (!paymentMethod)
        throw new NotFoundException('Payment method not found');
      return paymentMethod;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async setAsDefault(id: number, userId: number) {
    try {
      const paymentMethod =
        await this.paymentMethodRepository.getPaymentMethodById(id);
      if (!paymentMethod)
        throw new NotFoundException('Payment method not found');

      await this.paymentMethodRepository.resetAllDefaultAccounts(userId);

      const updated = await this.paymentMethodRepository.update(id, {
        idDefault: true,
      });
      return updated;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
  async activatePaymentMethod(id: number, userId: number) {
    try {
      const paymentMethod =
        await this.paymentMethodRepository.getPaymentMethodById(id);
      if (!paymentMethod)
        throw new NotFoundException('Payment method not found');
      if (paymentMethod.userId !== userId)
        throw new Error('You are not allowed to activate this account');

      await this.paymentMethodRepository.update(id, { isActive: true });
      return paymentMethod;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async deactivatePaymentMethod(id: number, userId: number) {
    try {
      const paymentMethod =
        await this.paymentMethodRepository.getPaymentMethodById(id);
      if (!paymentMethod)
        throw new NotFoundException('Payment method not found');
      if (paymentMethod.userId !== userId)
        throw new Error('You are not allowed to deactivate this account');

      await this.paymentMethodRepository.update(id, { isActive: false });
      return paymentMethod;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  // update account momo account
  async updatePaymentMethod(
    id: number,
    userId: number,
    updateDto: UpdateAccountDto,
  ) {
    try {
      const paymentMethod =
        await this.paymentMethodRepository.getPaymentMethodById(id);
      if (!paymentMethod)
        throw new NotFoundException('Payment method not found');
      if (paymentMethod.userId !== userId)
        throw new Error('You are not allowed to update this account');

      await this.paymentMethodRepository.update(id, {
        accountName: updateDto.accountName,
        accountNumber: updateDto.accountNumber,
        countryCode: updateDto.countryCode,
        provider: updateDto.provider,
      });
      return paymentMethod;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async verifyPaymentMethod(
    id: number,
    userId: number,
    verifyDto: VerifyAccountDto,
  ) {
    try {
      const paymentMethod =
        await this.paymentMethodRepository.getPaymentMethodById(id);
      if (!paymentMethod)
        throw new NotFoundException('Payment method not found');
      if (paymentMethod.userId !== userId)
        throw new Error('You are not allowed to verify this account');

      // verify otp
      const tokenData = await this.tokenRepository.findByToken(verifyDto.token);

      if (!tokenData) throw new UnauthorizedException('Invalid token');

      if (tokenData.expiresAt < new Date()) {
        await this.tokenRepository.delete(tokenData.id);
        throw new UnauthorizedException('Token expired');
      }

      if (tokenData.userId !== userId) {
        throw new UnauthorizedException('Token does not belong to this user');
      }

      await this.tokenRepository.delete(tokenData.id);

      await this.paymentMethodRepository.update(id, { isVerified: true });
      return paymentMethod;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async remove(id: number, userId: number) {
    const paymentMethod =
      await this.paymentMethodRepository.getPaymentMethodById(id);
    if (!paymentMethod) throw new NotFoundException('Payment method not found');
    if (paymentMethod.userId !== userId)
      throw new Error('You are not allowed to delete this account');
    return this.paymentMethodRepository.delete(id);
  }
}
