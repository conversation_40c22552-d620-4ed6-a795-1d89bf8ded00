import 'package:flutter/material.dart';
import 'package:${appName}${relative}/index.dart';


class ${upperName}Page extends StatefulWidget {
  const ${upperName}Page({
    required this.bloc,
    super.key
    });
  static const String routeName = '/${privateName}';
  
  final ${upperName}Bloc? bloc;

  @override
  State<${upperName}Page> createState() => _${upperName}PageState();
}

class _${upperName}PageState extends State<${upperName}Page> {

  ${upperName}Bloc? _bloc;
  ${upperName}Bloc get bloc {
    // get it by DI in real code.
    _bloc ??= widget.bloc ?? ${upperName}Bloc();
    return _bloc!;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text('${upperName}'),
         actions: [
          IconButton(
            icon: const Icon(Icons.error),
            onPressed: () {
              bloc.add(ErrorYouAwesomeEvent());
            },
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              bloc.add(Add${upperName}Event());
            },
          ),
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () {
              bloc.add(Clear${upperName}Event());
            },
          ),
        ],
      ),
      body: ${upperName}Screen(bloc: bloc),
    );
  }
}
