import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsInt } from 'class-validator';

export class CreateReportDto {
  @ApiProperty({
    description: 'ID of the user reporting the transaction',
    example: 1,
  })
  @IsNotEmpty()
  @IsInt()
  reporterId: number;

  @ApiProperty({
    description: 'ID of the transaction being reported',
    example: 1,
  })
  @IsNotEmpty()
  @IsInt()
  transactionId: number;

  @ApiProperty({
    description: 'Platform where the issue occurred',
    example: 'web',
  })
  @IsNotEmpty()
  @IsString()
  platform: string;

  @ApiProperty({
    description: 'Description of the report',
    example: 'User did not complete the transaction',
    required: false,
  })
  @IsString()
  description?: string;
}
