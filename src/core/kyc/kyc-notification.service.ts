import { Injectable, Logger } from '@nestjs/common';
import { EmailsService } from '../emails/emails.service';
import { NotificationsService } from '../notifications/notifications.service';

@Injectable()
export class KycNotificationService {
  private logger = new Logger(KycNotificationService.name);

  constructor(
    private readonly emailService: EmailsService,
    private readonly notificationsService: NotificationsService,
  ) {}

  /**
   * Send notification when KY<PERSON> is submitted
   */
  async notifyKycSubmitted(
    userId: number,
    kycId: number,
    userName: string,
    userEmail: string,
  ) {
    try {
      // Create push notification
      await this.notificationsService.create({
        title: 'KYC Submitted',
        message:
          'Your KYC verification has been submitted successfully and is under review.',
        userId: userId,
        type: 'personal',
        category: 'security_alerts',
        priority: 'pending',
        metadata: {
          model: 'kyc',
          kycId: kycId,
        },
      });

      // Send email notification
      await this.emailService.sendCustomEmail(
        userEmail,
        userName,
        'KYC Verification Submitted',
        'Thank you for submitting your KYC verification documents. Our team is reviewing your information and will notify you once the verification is complete. This process typically takes 24-48 hours.',
      );
    } catch (error) {
      this.logger.error('Failed to send KYC submitted notification', error);
    }
  }

  /**
   * Send notification when KYC is approved
   */
  async notifyKycApproved(
    userId: number,
    kycId: number,
    userName: string,
    userEmail: string,
  ) {
    try {
      // Create push notification
      await this.notificationsService.create({
        title: 'KYC Approved',
        message: 'Congratulations! Your KYC verification has been approved.',
        userId: userId,
        type: 'personal',
        category: 'security_alerts',
        priority: 'completed',
        metadata: {
          model: 'kyc',
          kycId: kycId,
        },
      });

      // Send email notification
      await this.emailService.sendCustomEmail(
        userEmail,
        userName,
        'KYC Verification Approved',
        'Congratulations! Your KYC verification has been approved. You now have full access to all platform features. Thank you for choosing our service.',
      );
    } catch (error) {
      this.logger.error('Failed to send KYC approved notification', error);
    }
  }

  /**
   * Send notification when KYC is rejected
   */
  async notifyKycRejected(
    userId: number,
    kycId: number,
    userName: string,
    userEmail: string,
    reason?: string,
  ) {
    try {
      const rejectionMessage = reason
        ? `Your KYC verification has been rejected. Reason: ${reason}`
        : 'Your KYC verification has been rejected. Please review and resubmit your documents.';

      // Create push notification
      await this.notificationsService.create({
        title: 'KYC Rejected',
        message: rejectionMessage,
        userId: userId,
        type: 'personal',
        category: 'security_alerts',
        priority: 'declined',
        metadata: {
          model: 'kyc',
          kycId: kycId,
          reason: reason,
        },
      });

      // Send email notification
      const emailMessage = reason
        ? `We regret to inform you that your KYC verification has been rejected. Reason: ${reason}. Please review the requirements and resubmit your documents with the correct information.`
        : 'We regret to inform you that your KYC verification has been rejected. Please review the requirements and resubmit your documents with the correct information.';

      await this.emailService.sendCustomEmail(
        userEmail,
        userName,
        'KYC Verification Rejected',
        emailMessage,
      );
    } catch (error) {
      this.logger.error('Failed to send KYC rejected notification', error);
    }
  }

  /**
   * Send notification when KYC is under review
   */
  async notifyKycUnderReview(
    userId: number,
    kycId: number,
    userName: string,
    userEmail: string,
  ) {
    try {
      // Create push notification
      await this.notificationsService.create({
        title: 'KYC Under Review',
        message: 'Your KYC verification is currently under review by our team.',
        userId: userId,
        type: 'personal',
        category: 'security_alerts',
        priority: 'pending',
        metadata: {
          model: 'kyc',
          kycId: kycId,
        },
      });

      // Send email notification
      await this.emailService.sendCustomEmail(
        userEmail,
        userName,
        'KYC Verification Under Review',
        'Your KYC verification documents are currently being reviewed by our team. We will notify you once the review is complete. Thank you for your patience.',
      );
    } catch (error) {
      this.logger.error('Failed to send KYC under review notification', error);
    }
  }

  /**
   * Send notification when additional documents are required
   */
  async notifyKycAdditionalDocumentsRequired(
    userId: number,
    kycId: number,
    userName: string,
    userEmail: string,
    requiredDocuments: string,
  ) {
    try {
      // Create push notification
      await this.notificationsService.create({
        title: 'Additional Documents Required',
        message: `Additional documents are required to complete your KYC verification: ${requiredDocuments}`,
        userId: userId,
        type: 'personal',
        category: 'security_alerts',
        priority: 'action_required',
        metadata: {
          model: 'kyc',
          kycId: kycId,
          requiredDocuments: requiredDocuments,
        },
      });

      // Send email notification
      await this.emailService.sendCustomEmail(
        userEmail,
        userName,
        'Additional Documents Required for KYC',
        `We need additional documents to complete your KYC verification. Please upload the following: ${requiredDocuments}. Once submitted, we will continue processing your verification.`,
      );
    } catch (error) {
      this.logger.error(
        'Failed to send KYC additional documents notification',
        error,
      );
    }
  }

  /**
   * Send notification when KYC is completed
   */
  async notifyKycCompleted(
    userId: number,
    kycId: number,
    userName: string,
    userEmail: string,
  ) {
    try {
      // Create push notification
      await this.notificationsService.create({
        title: 'KYC Completed',
        message: 'Your KYC verification has been completed successfully.',
        userId: userId,
        type: 'personal',
        category: 'security_alerts',
        priority: 'completed',
        metadata: {
          model: 'kyc',
          kycId: kycId,
        },
      });

      // Send email notification
      await this.emailService.sendCustomEmail(
        userEmail,
        userName,
        'KYC Verification Completed',
        'Your KYC verification process has been completed successfully. You now have full access to all features and services on our platform. Thank you for completing the verification process.',
      );
    } catch (error) {
      this.logger.error('Failed to send KYC completed notification', error);
    }
  }

  /**
   * Send reminder notification for pending KYC
   */
  async notifyKycPendingReminder(
    userId: number,
    userName: string,
    userEmail: string,
  ) {
    try {
      // Create push notification
      await this.notificationsService.create({
        title: 'KYC Verification Pending',
        message:
          'You have a pending KYC verification. Please complete it to access all features.',
        userId: userId,
        type: 'personal',
        category: 'reminders',
        priority: 'warning',
        metadata: {
          model: 'kyc',
        },
      });

      // Send email notification
      await this.emailService.sendCustomEmail(
        userEmail,
        userName,
        'Complete Your KYC Verification',
        'This is a reminder that your KYC verification is still pending. Please complete the verification process to unlock all features and services on our platform.',
      );
    } catch (error) {
      this.logger.error(
        'Failed to send KYC pending reminder notification',
        error,
      );
    }
  }
}
