import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:${appName}${relative}/index.dart';


class ${upperName}Screen extends StatefulWidget {
  const ${upperName}Screen({
    required this.bloc,
    super.key,
  }) ;

  @protected
  final ${upperName}Bloc bloc;

  @override
  State<${upperName}Screen> createState() {
    return ${upperName}ScreenState();
  }
}

class ${upperName}ScreenState extends State<${upperName}Screen> {

  @override
  void initState() {
    super.initState();
    // load data on init widget if bloc has not data
    if (!widget.bloc.state.hasData) {
      _load();
    }
  }

  @override
  void dispose() {
    // dispose bloc if you use subscriptions in bloc
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<${upperName}Bloc, ${upperName}State>(
      bloc: widget.bloc,
      builder: (
        BuildContext context,
        ${upperName}State currentState,
      ) {
        // declaration of bloc states
        return currentState.when(
          onLoading: ()=>const CircularProgressIndicator(),
          onEmpty: (data) =>  _Empty(),
          onData: (data) =>  _BodyList(data: data),
          onError: (e) =>  Center(
            child: Column(
              children: [
                Text(e.toString()),
                TextButton(
                  onPressed: _load,
                  child: const Text('ReLoad'),
                )
              ],
            ),
          ),
        );
      },
    );
  }

  void _load() {
    widget.bloc.add(Load${upperName}Event(id:'1'));
  }

}


class _BodyList extends StatefulWidget {
  const _BodyList({required this.data});

  final ${upperName}ViewModel data;

  @override
  State<_BodyList> createState() => _BodyListState();
}

class _BodyListState extends State<_BodyList> {

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {

    return CustomScrollView(
        // primary: true,
        slivers: [
          const SliverToBoxAdapter(child: Divider()),
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (BuildContext context, int index) {
          final item = widget.data.items![index];
          if (index == 0) {
            return Text('Header $index, id = '+item.name);
          }
          return Text('Index = $index, id = '+item.name);
        },
        childCount: widget.data.items!.length,
    ))]);
  }
}


class _Empty extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Text('Empty'),
      ],
    );
  }
}