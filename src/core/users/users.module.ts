import { Module } from '@nestjs/common';
import { UsersService } from './users.service';
import {
  PreferenceRepository,
  ProfileRepository,
  UserRepository,
} from 'src/common/repository';
import { UsersController } from './users.controller';
@Module({
  providers: [
    UsersService,
    UserRepository,
    ProfileRepository,
    PreferenceRepository,
  ],
  controllers: [UsersController],
})
export class UsersModule {}
