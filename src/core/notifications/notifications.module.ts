import { Global, Module } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { NotificationRepository, UserRepository } from 'src/common/repository';
import { NotificationsController } from './notifications.controller';
import { PushNotificationService } from './pushnotification.service';
import { AdminNotification } from 'src/webpush-notifications/admin-notifiction.gateway';
import { AdminNotificationService } from './admin-notification.service';

@Global()
@Module({
  controllers: [NotificationsController],
  providers: [
    NotificationsService,
    NotificationRepository,
    AdminNotification,
    UserRepository,
    PushNotificationService,
    AdminNotificationService,
  ],
  exports: [NotificationsService, AdminNotificationService],
})
export class NotificationsModule {}
