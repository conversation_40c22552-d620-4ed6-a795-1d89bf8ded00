/* eslint-disable @typescript-eslint/no-unsafe-return */
import { Controller, Get, UseGuards } from '@nestjs/common';
import { ReferralService } from './referral.service';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/common/guards/jwt.guard';
import { User } from 'src/common/decorators/user.decorator';

@ApiTags('Referral')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('referral')
export class ReferralController {
  constructor(private readonly referralService: ReferralService) {}

  @Get('code')
  @ApiOperation({ summary: 'Get user referral code' })
  @ApiResponse({
    status: 200,
    description: 'Returns user referral code',
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getCode(@User('id') userId: number) {
    return this.referralService.getCode(userId);
  }

  @Get('my-referrals')
  @ApiOperation({ summary: 'Get all referrals made by current user' })
  @ApiResponse({
    status: 200,
    description: 'Returns list of referrals made by the user',
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getReferrals(@User('id') userId: number) {
    return this.referralService.getReferrals(userId);
  }

  @Get('referred-users')
  @ApiOperation({ summary: 'Get all users referred by current user' })
  @ApiResponse({
    status: 200,
    description: 'Returns list of users referred by the current user',
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getReferredUsers(@User('id') userId: number) {
    return this.referralService.getReferredUsers(userId);
  }

  @Get('who-referred-me')
  @ApiOperation({ summary: 'Get who referred the current user' })
  @ApiResponse({
    status: 200,
    description: 'Returns information about who referred the user',
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getWhoReferredUser(@User('id') userId: number) {
    return this.referralService.getWhoReferredUser(userId);
  }

  @Get('details')
  @ApiOperation({ summary: 'Get referral details with user information' })
  @ApiResponse({
    status: 200,
    description: 'Returns detailed referral information',
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getReferalsWithDetails(@User('id') userId: number) {
    return this.referralService.getReferalsWithDetails(userId);
  }
}
