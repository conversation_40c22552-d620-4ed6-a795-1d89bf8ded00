import { PaymentMethod, NewPaymentMethod } from '../../schemas';
import { IRepository } from './repository.interface';

export interface IPaymentMethodRepository extends IRepository<PaymentMethod> {
  createMtnPaymentMethod(
    userId: number,
    data: Partial<NewPaymentMethod>,
  ): Promise<Partial<PaymentMethod>>;
  createOrangePaymentMethod(
    userId: number,
    data: Partial<NewPaymentMethod>,
  ): Promise<Partial<PaymentMethod>>;
  createBankPaymentMethod(
    userId: number,
    data: Partial<NewPaymentMethod>,
  ): Promise<Partial<PaymentMethod>>;
  getAllUserPaymentMethods(userId: number): Promise<PaymentMethod[] | null>;

  getPaymentMethodById(
    id: number,
    userId: number,
  ): Promise<PaymentMethod | null>;

  getUsersDefautltPaymentMethod(userId: number): Promise<PaymentMethod | null>;

  findByAccountNumber(accountNumber: string): Promise<PaymentMethod | null>;

  resetAllDefaultAccounts(userId: number): Promise<void>;
}
