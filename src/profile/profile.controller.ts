import {
  Body,
  Controller,
  Get,
  Patch,
  Post,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ProfileService } from './profile.service';
import { User } from 'src/common/decorators/user.decorator';
import { UpdateProfileDto } from './dto/update.dto';
import { JwtAuthGuard } from 'src/common/guards/jwt.guard';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiBearerAuth,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { UpdatePreferenceDto } from './dto/preference.dto';
import { FileUploadDto } from './dto/image.dto';
import { CheckUserDto } from './dto/check-user.dto';

@ApiTags('Profile')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('profile')
export class ProfileController {
  constructor(private readonly profileService: ProfileService) {}

  @Get()
  @ApiOperation({ summary: 'Get user profile' })
  @ApiResponse({
    status: 200,
    description: 'Returns user profile',
    // type: UserProfileEntity,
  })
  @ApiResponse({ status: 404, description: 'Profile not found' })
  async getProfile(@User('id') userId: number) {
    const profile = await this.profileService.getProfile(userId);
    return profile;
  }

  @Post('check')
  @ApiOperation({ summary: 'Check user profile' })
  @ApiResponse({
    status: 200,
    description: 'Returns user profile',
  })
  @ApiResponse({ status: 404, description: 'Profile not found' })
  async checkUserProfile(@Body() dto: CheckUserDto) {
    return this.profileService.checkUserProfile(dto.code);
  }

  @Patch('image')
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Update profile image' })
  @ApiResponse({
    status: 200,
    description: 'Profile image updated successfully',
  })
  @ApiBody({ type: FileUploadDto })
  @ApiResponse({ status: 404, description: 'Profile not found' })
  @UseInterceptors(FileInterceptor('image'))
  async updateProfileImage(
    @User('id') userId: number,
    @UploadedFile() image: Express.Multer.File,
  ) {
    return this.profileService.updateProfileImage(userId, image);
  }

  @Patch()
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Update user profile' })
  @ApiResponse({ status: 200, description: 'Profile updated successfully' })
  @ApiResponse({ status: 404, description: 'Profile not found' })
  @UseInterceptors(FileInterceptor('image'))
  async updateProfile(
    @User('id') userId: number,
    @Body() profileData: UpdateProfileDto,
    @UploadedFile() image: Express.Multer.File,
  ) {
    return this.profileService.updateProfile(userId, profileData, image);
  }

  @Get('preferences')
  @ApiOperation({ summary: 'Get user preferences' })
  @ApiResponse({ status: 200, description: 'Returns user preferences' })
  @ApiResponse({ status: 404, description: 'Preferences not found' })
  async getPreferences(@User('id') userId: number) {
    return this.profileService.getPreferences(userId);
  }

  @Patch('preferences')
  @ApiOperation({ summary: 'Update user preferences' })
  @ApiResponse({ status: 200, description: 'Preferences updated successfully' })
  @ApiResponse({ status: 404, description: 'Preferences not found' })
  async updatePreferences(
    @User('id') userId: number,
    @Body() preferencesData: UpdatePreferenceDto,
  ) {
    return this.profileService.updatePreferences(userId, preferencesData);
  }
}
