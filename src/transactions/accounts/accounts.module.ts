import { Module } from '@nestjs/common';
import { AccountsService } from './accounts.service';
import { AccountsController } from './accounts.controller';
import {
  UserRepository,
  PaymentMethodRepository,
  TokenRepository,
} from 'src/common/repository';

@Module({
  controllers: [AccountsController],
  providers: [
    AccountsService,
    UserRepository,
    PaymentMethodRepository,
    TokenRepository,
  ],
})
export class AccountsModule {}
