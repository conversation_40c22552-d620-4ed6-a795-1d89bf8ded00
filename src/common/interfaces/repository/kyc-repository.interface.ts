/* eslint-disable @typescript-eslint/no-redundant-type-constituents */
import { NewKyc, Kyc, File } from '../../schemas';
import { PaginatedResponse, PaginationQuery } from '../paginaton';

export interface IKycRepository {
  create(kycData: NewKyc): Promise<Kyc>;
  update(id: number, kycData: Partial<Kyc>): Promise<Kyc | null>;
  findByUserId(userId: number): Promise<any | null>;
  findById(id: number): Promise<Kyc | null>;
  getAll(query: PaginationQuery): Promise<PaginatedResponse<any>>;
  delete(id: number): Promise<boolean>;
}
