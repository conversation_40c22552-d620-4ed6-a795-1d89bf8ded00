import { Module } from '@nestjs/common';
import { EscrowService } from './escrow.service';
import { EscrowController } from './escrow.controller';
import { EscrowRepository } from 'src/common/repository/escrow.repository';
import { SystemSettingsRepository } from 'src/common/repository/system-settings.repository';

@Module({
  controllers: [EscrowController],
  providers: [EscrowService, EscrowRepository, SystemSettingsRepository],
})
export class EscrowModule {}
