import { Module } from '@nestjs/common';
import {
  PreferenceRepository,
  ProfileRepository,
  TransactionRepository,
  UserRepository,
} from 'src/common/repository';
import { ProfileController } from './profile.controller';
import { ProfileService } from './profile.service';

@Module({
  providers: [
    ProfileRepository,
    UserRepository,
    ProfileService,
    PreferenceRepository,
    TransactionRepository,
  ],
  controllers: [ProfileController],
})
export class ProfileModule {}
