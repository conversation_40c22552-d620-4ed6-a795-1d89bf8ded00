import { PartialType } from '@nestjs/mapped-types';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { CreateReportDto } from './create-report.dto';

export class UpdateReportDto extends PartialType(CreateReportDto) {
  @ApiProperty({
    description: 'Status of the report',
    example: 'resolved',
    enum: ['pending', 'resolved', 'rejected'],
    required: false,
  })
  @IsOptional()
  @IsEnum(['pending', 'resolved', 'rejected'])
  status?: 'pending' | 'reviewed' | 'resolved' | 'dismissed';
}
