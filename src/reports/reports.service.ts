import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { TransactionReportRepository } from 'src/common/repository/transaction-report.repository';
import { CreateReportDto } from './dto/create-report.dto';
import { PaginationQuery } from 'src/common/interfaces';
import { UpdateReportDto } from './dto/update-report.dto';
import { AdminNotification } from 'src/webpush-notifications/admin-notifiction.gateway';

@Injectable()
export class ReportsService {
  private logger = new Logger(ReportsService.name);
  constructor(
    private readonly report: TransactionReportRepository,
    private readonly webPush: AdminNotification,
  ) {}
  async createReport(reportData: CreateReportDto) {
    try {
      const report = await this.report.create(reportData);
      //send admin push notification
      this.webPush
        .sendNotificationToAdmins({
          title: 'New Report',
          message: `A new report has been created`,
          type: 'info',
          data: {
            reportId: report.id,
          },
        })
        .catch((err) => {
          this.logger.error(err);
        });
      return report;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getReportsByTransactionId(transactionId: number) {
    try {
      return await this.report.findByTransactionId(transactionId);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getReportsByUserId(userId: number, query: PaginationQuery) {
    try {
      return await this.report.findByUserId(userId, query);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
  async getReport(id: number) {
    try {
      const report = await this.report.findById(id);
      if (!report) throw new NotFoundException();
      return report;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
  async getAllReports(query: PaginationQuery) {
    try {
      return await this.report.findAll(query);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
  async updateReportStatus(id: number, data: UpdateReportDto) {
    try {
      const report = await this.report.findById(id);
      if (!report) throw new NotFoundException();
      const updated = await this.report.update(id, data);
      return updated;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
}
