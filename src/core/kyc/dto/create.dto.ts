import { ApiProperty } from '@nestjs/swagger';
import {
  IsDateString,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateKycDto {
  @ApiProperty({
    example: 'United States',
    description: 'Country of residence',
  })
  @IsString()
  @IsNotEmpty()
  country: string;

  @ApiProperty({ example: '1990-01-01', description: 'Date of birth' })
  @IsDateString()
  @IsOptional()
  dob?: Date;

  @ApiProperty({ example: '+1234567890', description: 'Phone number' })
  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @ApiProperty({
    example: '123 Main St, City, Country',
    description: 'Physical address',
  })
  @IsString()
  @IsOptional()
  address?: string;
}
