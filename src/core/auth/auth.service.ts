import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import {
  ProfileRepository,
  UserRepository,
  TokenRepository,
  PreferenceRepository,
} from 'src/common/repository';
import { LoginDto } from './dto/login.dto';
import { User } from 'src/common/schemas';
import { SignUpDto } from './dto/signup.dto';
import { EmailDto } from './dto/email.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import {
  generateOTPCode,
  generateRandomCode,
  referralCode,
} from 'src/common/utils';
import { TokenDto } from './dto/token.dto';
import { EmailsService } from '../emails/emails.service';
import { GoogleUserDto } from './dto/google-user.dto';
import { GoogleUserInfoV1Response } from 'src/common/interfaces';
import { ReferalRepository } from 'src/common/repository/referal.repository';
import { NotificationsService } from '../notifications/notifications.service';
import { AdminNotification } from 'src/webpush-notifications/admin-notifiction.gateway';

@Injectable()
export class AuthService {
  private logger = new Logger(AuthService.name);
  constructor(
    private readonly usersRepository: UserRepository,
    private readonly referalRepository: ReferalRepository,
    private readonly profileRepository: ProfileRepository,
    private readonly tokenRepository: TokenRepository,
    private readonly jwtService: JwtService,
    private readonly emailService: EmailsService,
    private readonly preferenceRepository: PreferenceRepository,
    private readonly notificationsService: NotificationsService,
    private readonly webPush: AdminNotification,
  ) {}

  async validateUser(dto: LoginDto) {
    const user = await this.usersRepository.findByEmail(dto.email);
    if (!user) throw new UnauthorizedException('Invalid credentials');

    if (user.isBanned) throw new UnauthorizedException('User is banned');
    if (user.isDeleted) throw new UnauthorizedException('User is deleted');
    if (!user.isActive) throw new UnauthorizedException('User is not active');

    if (user.password === null)
      throw new UnauthorizedException(
        'Please use ' + user.provider + ' to login',
      );
    const isPasswordValid = await bcrypt.compare(dto.password, user.password);
    if (!isPasswordValid)
      throw new UnauthorizedException('Invalid credentials');
    this.notificationsService
      .create({
        title: 'Login',
        message: 'User logged in successfully',
        userId: user.id,
        type: 'personal',
        category: 'security_alerts',
        priority: 'info',
        metadata: {
          model: 'auth',
          // ip: req?.ip,
          // location: req?.ip,
          // browser: req?.headers['user-agent'],
        },
      })
      .catch((err) => {
        this.logger.error('Failed to create notification', err);
      });
    const token = this.login(user);
    return {
      ...user,
      ...token,
    };
  }

  async signUp(dto: SignUpDto) {
    try {
      const existingUser = await this.usersRepository.findByEmail(dto.email);
      if (existingUser && (existingUser.isBanned || existingUser.isDeleted))
        throw new BadRequestException(
          'Please contact support to restore your account',
        );

      if (existingUser) throw new BadRequestException('Email already exists');

      const hashedPassword = await bcrypt.hash(dto.password, 10);
      const user = await this.usersRepository.create({
        name: dto.name,
        email: dto.email,
        password: hashedPassword,
        referralCode: referralCode(),
        bannedAt: undefined,
        deletedAt: undefined,
        verifiedAt: undefined,
      });
      const code = generateRandomCode(6);

      if (!user) return;
      const profile = await this.profileRepository.create({
        avatar: 'https://avatar.iran.liara.run/public/37',
        firstName: dto.name.split(' ')[0],
        lastName: dto.name.split(' ')[1],
        userId: user.id,
        code,
      });

      if (dto.refCode) {
        const referrer = await this.usersRepository.findUserByReferralCode(
          dto.refCode,
        );
        if (!referrer) throw new NotFoundException('Wrong reffreal code');
        await this.referalRepository.create({
          userId: user.id,
          referredById: referrer.id,
          referralCode: dto.refCode,
        });
      }
      await this.preferenceRepository.create(user.id!);

      this.emailService
        .sendWelcomeEmail(user.email!, user.name!)
        .catch((err) => {
          this.logger.error('Failed to send welcome email', err);
        });

      this.notificationsService
        .create({
          title: 'Welcome to Hold EX!',
          message: `Welcome to Transacta, ${user.name}! We're glad you're here.`,
          userId: user.id,
          type: 'personal',
          category: 'general_updates',
          priority: 'info',
          metadata: {
            model: 'auth',
          },
        })
        .catch((notification) => {
          this.logger.log('Notification created', notification);
        });
      await this.webPush.sendNotificationToAdmins({
        title: 'New User',
        message: `New user ${user.name} has signed up${dto.refCode ? ' with referral code' : ''}`,
        type: 'info',
        data: {
          userId: user.id,
          date: user.createdAt,
        },
      });
      return { user, profile };
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  async resetPassword({ token, password }: ResetPasswordDto) {
    const tokenData = await this.tokenRepository.findByToken(token);

    if (!tokenData) throw new UnauthorizedException('Invalid token');

    if (tokenData.expiresAt < new Date()) {
      await this.tokenRepository.delete(tokenData.id);
      throw new UnauthorizedException('Token expired');
    }

    const user = await this.usersRepository.update(tokenData.userId!, {
      password: await bcrypt.hash(password, 10),
    });

    if (user) await this.tokenRepository.delete(tokenData.id);

    if (user) {
      this.notificationsService
        .create({
          title: 'Password Reset',
          message: 'Your password has been reset successfully',
          userId: user.id,
          type: 'personal',
          category: 'security_alerts',
          priority: 'completed',
          metadata: {
            model: 'auth',
          },
        })
        .catch((notification) => {
          this.logger.log('Notification created', notification);
        });
    }

    return user;
  }

  async forgotPassword({ email }: EmailDto) {
    const user = await this.usersRepository.findByEmail(email);
    if (!user) throw new NotFoundException('User not found');

    const token = await this.tokenRepository.create({
      userId: user.id,
      token: generateOTPCode(6),
      type: 'reset_password',
      expiresAt: new Date(Date.now() + 60 * 60 * 1000),
    });

    if (token) {
      await this.emailService.sendResetPasswordEmail(
        user.email,
        user.name,
        token.token,
      );
    }

    return { message: 'Password reset token sent' };
  }

  async sendVerificationToken({ email }: EmailDto) {
    const user = await this.usersRepository.findByEmail(email);
    if (!user) throw new NotFoundException('User not found');

    const token = await this.tokenRepository.create({
      userId: user.id,
      token: generateOTPCode(6),
      type: 'verification',
      expiresAt: new Date(Date.now() + 60 * 60 * 1000),
    });

    if (token) {
      // await this.emailService.sendVerificationEmail(
      //   user.email,
      //   user.name,
      //   token.token,
      // );
    }

    return { message: 'Verification token sent', token };
  }

  async verifyUsersAccount({ token }: TokenDto) {
    const tokenData = await this.tokenRepository.findByTokenWithUser(token);

    if (!tokenData) throw new UnauthorizedException('Invalid token');

    if (tokenData.token.expiresAt < new Date()) {
      await this.tokenRepository.delete(tokenData.token.id);
      throw new UnauthorizedException('Token expired');
    }

    const user = await this.usersRepository.update(tokenData.token.userId!, {
      isVerified: true,
      isActive: true,
      isEmailVerified: true,
      verifiedAt: new Date(),
    });

    if (user) await this.tokenRepository.delete(tokenData.token.id);

    return user;
  }

  async findOrCreateGoogleUser(googleUser: GoogleUserDto) {
    const existingUser = await this.usersRepository.findByEmail(
      googleUser.email,
    );

    if (existingUser && existingUser.googleId) {
      return existingUser;
    }

    let user: Partial<User> | null = null;
    if (!existingUser) {
      user = await this.usersRepository.create({
        email: googleUser.email,
        googleId: googleUser.googleId,
        role: 'user',
        name: googleUser.firstName + ' ' + googleUser.lastName,
        password: null,
      });
    } else {
      user = await this.usersRepository.update(existingUser.id, {
        googleId: googleUser.googleId,
        provider: 'google',
      });
    }

    if (!user) return;

    const profile = await this.profileRepository.findByUserId(
      user.id as number,
    );
    if (!profile) {
      await this.profileRepository.create({
        firstName: googleUser.firstName,
        lastName: googleUser.lastName,
        userId: user.id,
        avatar: googleUser.picture,
      });
    }

    const preference = await this.preferenceRepository.findByUserId(
      user.id as number,
    );
    if (!preference) {
      await this.preferenceRepository.create(user.id as number);
    }

    return user;
  }

  async validateGoogleToken(token: string) {
    try {
      const response = await fetch(
        `https://www.googleapis.com/oauth2/v1/userinfo?access_token=${token}`,
      );
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);

      const profile = (await response.json()) as GoogleUserInfoV1Response;
      return await this.findOrCreateGoogleUser({
        googleId: profile.id,
        email: profile.email,
        firstName: profile.given_name,
        lastName: profile.family_name,
        picture: profile.picture,
      });
    } catch (error) {
      throw new UnauthorizedException('Invalid Google token', error as Error);
    }
  }

  async sendPhoneVerificationToken(phone: string) {
    const user = await this.usersRepository.findByPhone(phone);
    if (!user) throw new NotFoundException('User not found');

    const token = await this.tokenRepository.create({
      userId: user.id,
      token: generateOTPCode(6),
      type: 'phone_verification',
      expiresAt: new Date(Date.now() + 60 * 60 * 1000),
    });

    // TODO: Integrate SMS service to send verification code
    // await this.smsService.sendVerificationCode(phone, token.token);

    return { message: 'Phone verification token sent', token };
  }

  async verifyPhoneNumber({ token }: TokenDto) {
    const tokenData = await this.tokenRepository.findByTokenWithUser(token);

    if (!tokenData) throw new UnauthorizedException('Invalid token');

    if (tokenData.token.expiresAt < new Date()) {
      await this.tokenRepository.delete(tokenData.token.id);
      throw new UnauthorizedException('Token expired');
    }
    if (tokenData.user.isPhoneVerified)
      throw new BadRequestException('Phone already verified');

    const user = await this.usersRepository.update(tokenData.token.userId!, {
      isPhoneVerified: true,
    });

    if (user) await this.tokenRepository.delete(tokenData.token.id);

    if (user) {
      this.notificationsService
        .create({
          title: 'Phone Verified',
          message: 'Your phone number has been verified successfully',
          userId: user.id,
          type: 'personal',
          category: 'security_alerts',
          priority: 'completed',
          metadata: {
            model: 'auth',
          },
        })
        .catch((err) => {
          this.logger.error('Failed to create notification', err);
        });
    }

    return user;
  }

  login(user: Partial<User>) {
    const payload = {
      id: user.id,
      email: user.email,
      role: user.role,
      verified: user.isVerified,
    };
    return {
      access_token: this.jwtService.sign(payload),
    };
  }

  async testAdminNotification() {
    return this.webPush.sendNotificationToAdmins({
      title: 'Test',
      message: 'Test',
      type: 'info',
      data: {
        test: 'test',
      },
    });
  }
}
