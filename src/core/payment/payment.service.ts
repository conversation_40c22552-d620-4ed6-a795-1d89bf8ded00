import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import {
  PaymentMethodRepository,
  PaymentRepository,
  TransactionRepository,
  UserRepository,
} from 'src/common/repository';
import { NewPayment } from 'src/common/schemas';
import { PaginationQuery } from 'src/common/interfaces';
import { CampayService } from './campay/campay.service';

@Injectable()
export class PaymentService {
  constructor(
    private readonly paymentRepository: PaymentRepository,
    private readonly paymentMethodRepository: PaymentMethodRepository,
    private readonly transactionRepository: TransactionRepository,
    private readonly campayService: CampayService,
    private readonly usersRepository: UserRepository,
  ) {}

  async createPayment(paymentData: NewPayment) {
    try {
      if (!paymentData.transactionId)
        throw new BadRequestException('Transaction ID is required');

      const transaction = await this.transactionRepository.findById(
        paymentData.transactionId,
      );
      if (!transaction) throw new NotFoundException('Transaction not found');

      if (!transaction.paymentMethodId) {
        throw new BadRequestException('Transaction has no payment method');
      }

      const paymentMethod = await this.paymentMethodRepository.findById(
        transaction.paymentMethodId,
      );
      if (!paymentMethod)
        throw new NotFoundException('Payment method not found');
      if (!paymentMethod.isActive)
        throw new BadRequestException('Payment method is not active');
      // based on the payment method type, call the appropriate payment gateway
      // for now, we will just create a payment record
      const payment = await this.paymentRepository.create(paymentData);

      if (paymentMethod.type === 'mobile_money') {
        // call mtn api
        //
      }

      if (paymentMethod.type === 'master_card') {
        // call master card api
      }

      if (paymentMethod.type === 'visa_card') {
        // call visa card api
      }

      // notify all parties
      return payment;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getPaymentMethods(transactionId: number) {
    try {
      const payments =
        await this.paymentRepository.findByTransactionId(transactionId);
      return payments;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async initiate(transactionId: number, userId: number) {
    try {
      const transaction =
        await this.transactionRepository.findById(transactionId);
      if (!transaction) throw new NotFoundException('Transaction not found');

      if (!transaction.paymentMethodId) {
        throw new BadRequestException('Transaction has no payment method');
      }

      // get initiator
      const initiator = await this.usersRepository.findById(
        transaction.initiatedBy!,
      );

      if (!initiator) throw new NotFoundException('Initiator user not found');

      if (initiator.id !== userId) throw new ForbiddenException();

      const paymentMethod = await this.paymentMethodRepository.findById(
        transaction.paymentMethodId,
      );
      if (!paymentMethod)
        throw new NotFoundException('Payment method not found');
      if (!paymentMethod.isActive)
        throw new BadRequestException('Payment method is not active');

      if (
        paymentMethod.accountNumber &&
        paymentMethod.type === 'mobile_money'
      ) {
        await this.campayService.collectPayment({
          amount: parseFloat(transaction.amount),
          phone: paymentMethod.accountNumber.replace('+', '').trim(),
          description: transaction.title,
          email: initiator.email,
          direction: 'to_escrow',
          transactionId: transaction.id,
          paymentMethodId: paymentMethod.id,
        });
      }
      return transaction;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
  async getAll(query: PaginationQuery) {
    try {
      const payments = await this.paymentRepository.findAll(query);
      return payments;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
}
