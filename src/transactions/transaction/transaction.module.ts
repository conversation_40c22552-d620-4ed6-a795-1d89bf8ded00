import { Module } from '@nestjs/common';
import { TransactionController } from './transaction.controller';
import { TransactionService } from './transaction.service';
import {
  TransactionRepository,
  UserRepository,
  PaymentMethodRepository,
  PaymentRepository,
  KycRepository,
} from 'src/common/repository';
import { EmailsService } from 'src/core/emails/emails.service';
import { NotificationsService } from 'src/core/notifications/notifications.service';
import { NotificationRepository } from 'src/common/repository';
import { FilesService } from 'src/core/files/files.service';
import { FileRepository } from 'src/common/repository';
import { EscrowRepository } from 'src/common/repository/escrow.repository';
import { PaymentModule } from 'src/core/payment/payment.module';
import { CampayService } from 'src/core/payment/campay/campay.service';
import { HttpModule } from '@nestjs/axios';
import { TransactionNotificationService } from 'src/core/payment/campay/transaction-notification.service';
import { PushNotificationService } from 'src/core/notifications/pushnotification.service';
import { AdminNotification } from 'src/webpush-notifications/admin-notifiction.gateway';
@Module({
  imports: [PaymentModule, HttpModule],
  controllers: [TransactionController],
  providers: [
    TransactionService,
    TransactionRepository,
    UserRepository,
    PaymentMethodRepository,
    PaymentRepository,
    CampayService,
    EmailsService,
    NotificationsService,
    AdminNotification,
    PushNotificationService,
    TransactionNotificationService,
    FilesService,
    EscrowRepository,
    NotificationRepository,
    FileRepository,
    KycRepository,
  ],
})
export class TransactionModule {}
