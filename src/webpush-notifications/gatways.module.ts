import { Global, Module } from '@nestjs/common';
import { UserRepository } from 'src/common/repository';
import { AdminNotification } from './admin-notifiction.gateway';
import { NotificationQueueService } from './notification-queue.service';

@Global()
@Module({
  providers: [UserRepository, AdminNotification, NotificationQueueService],
  exports: [NotificationQueueService, AdminNotification],
})
export class GatewayModule {}
