import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import {
  EscrowRepository,
  NotificationRepository,
  PaymentMethodRepository,
  PaymentRepository,
  TransactionRepository,
  UserRepository,
} from 'src/common/repository';
import { TransactionEntity } from 'src/transactions/transaction/entity/tranaction';
import { NotificationsService } from '../notifications/notifications.service';
import { PaymentService } from './payment.service';
import { PaymentController } from './payment.controller';
import { CampayService } from './campay/campay.service';
import { TransactionNotificationService } from './campay/transaction-notification.service';
import { WebhookService } from './campay/webhook.service';
import { PushNotificationService } from '../notifications/pushnotification.service';
import { AdminNotification } from 'src/webpush-notifications/admin-notifiction.gateway';
import { AdminNotificationService } from '../notifications/admin-notification.service';

@Module({
  imports: [HttpModule],
  controllers: [PaymentController],
  providers: [
    PaymentRepository,
    PaymentMethodRepository,
    TransactionRepository,
    TransactionEntity,
    NotificationsService,
    NotificationRepository,
    EscrowRepository,
    WebhookService,
    TransactionNotificationService,
    UserRepository,
    PaymentService,
    CampayService,
    PushNotificationService,
    AdminNotification,
    AdminNotificationService,
  ],
  exports: [PaymentService],
})
export class PaymentModule {}
