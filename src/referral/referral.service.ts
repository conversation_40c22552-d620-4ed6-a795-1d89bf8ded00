/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-return */
import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { UserRepository } from 'src/common/repository';
import { ReferalRepository } from 'src/common/repository/referal.repository';
import { referralCode } from 'src/common/utils';

@Injectable()
export class ReferralService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly referalRepository: ReferalRepository,
  ) {}

  async getCode(userId: number) {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) throw new NotFoundException('User not found');

      if (!user.referralCode) {
        const code = referralCode();
        await this.userRepository.update(userId, {
          referralCode: code,
        });
        return { referralCode: code };
      }

      return { referralCode: user.referralCode };
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getReferrals(userId: number) {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) throw new NotFoundException('User not found');

      const referrals = await this.referalRepository.getUserRefarrals(userId);

      return referrals;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
  async getReferredUsers(userId: number) {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) throw new NotFoundException('User not found');

      const referredUsers =
        await this.referalRepository.getReferredUsers(userId);

      return referredUsers;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getWhoReferredUser(userId: number) {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) throw new NotFoundException('User not found');

      const whoReferredUser =
        await this.referalRepository.getWhoReferredUser(userId);

      return whoReferredUser;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getReferalsWithDetails(userId: number) {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) throw new NotFoundException('User not found');

      const referalsWithDetails =
        await this.referalRepository.getReferralWithDetails(userId);

      return referalsWithDetails;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
}
