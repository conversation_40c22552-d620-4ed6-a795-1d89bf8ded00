import { NewReferal, Referal } from '../../schemas';

export interface IReferalRepository {
  create(referalData: NewReferal): Promise<Referal>;
  findByUserId(userId: number): Promise<Referal | null>;
  findByReferredById(referredById: number): Promise<Referal | null>;
  getUserRefarrals(userId: number): Promise<Referal[]>;
  getReferredUsers(referredById: number): Promise<Referal[]>;
  getWhoReferredUser(userId: number): Promise<Referal | null>;
  getReferralWithDetails(userId: number): Promise<Referal | null>;
}
