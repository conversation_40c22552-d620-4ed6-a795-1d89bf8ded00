import { Transaction } from 'src/common/schemas';
import { ApiProperty } from '@nestjs/swagger';

export class TransactionEntity implements Transaction {
  @ApiProperty()
  id: number;

  @ApiProperty()
  title: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  aggrement: string;

  @ApiProperty()
  category: string;

  @ApiProperty()
  deadLine: Date;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  initiatedBy: number;

  @ApiProperty()
  receivedBy: number;

  @ApiProperty()
  paymentMethodId: number;

  @ApiProperty()
  amount: string;

  @ApiProperty()
  status:
    | 'completed'
    | 'accepted'
    | 'disputed'
    | 'cancelled'
    | 'expired'
    | 'declined'
    | 'pending'
    | 'failed';

  constructor(data: Partial<TransactionEntity>) {
    Object.assign(this, data);
  }
  message: string | null;
  transactionId: string;
}
