export interface CampayTokenResponse {
  token: string;
  expires_in?: number;
}

export interface CampayCollectRequest {
  amount: string;
  from: string;
  description: string;
  external_reference: string;
}

export interface CampayDisbursementRequest {
  amount: string;
  to: string;
  description: string;
  external_reference: string;
}

export interface CampayTransactionResponse {
  reference: string;
  external_reference: string;
  status: string;
  amount: string;
  currency: string;
  operator: string;
  code: string;
  operator_reference?: string;
  description?: string;
  reason?: string;
}

export interface CampayWebhookPayload {
  reference: string;
  external_reference: string;
  status: 'SUCCESSFUL' | 'FAILED' | 'PENDING';
  amount: string;
  currency: string;
  operator: string;
  code: string;
  operator_reference?: string;
  reason?: string;
  description?: string;
}

export interface CollectPaymentDto {
  amount: number;
  phone: string;
  description?: string;
}

export interface DisbursementDto {
  amount: number;
  phone: string;
  description?: string;
}
