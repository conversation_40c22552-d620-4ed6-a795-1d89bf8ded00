import { Module, MiddlewareConsumer } from '@nestjs/common';
import { DatabaseModule } from './database/database.module';
import { ConfigModule } from '@nestjs/config';
import { CoreModule } from './core/core.module';
import { ProfileModule } from './profile/profile.module';
import { RequestMiddleware } from './common/middleware/request.middleware';
import { AccountsModule } from './transactions/accounts/accounts.module';
import { TransactionsModule } from './transactions/transactions.module';
import { SystemSettingsModule } from './system-settings/system-settings.module';
import { CacheModule } from '@nestjs/cache-manager';
import { ReferralModule } from './referral/referral.module';
import { GatewayModule } from './webpush-notifications/gatways.module';
import { ReportsModule } from './reports/reports.module';

@Module({
  imports: [
    ConfigModule.forRoot({ envFilePath: '.env', isGlobal: true }),
    CacheModule.register({
      ttl: 3600,
      isGlobal: true,
    }),
    DatabaseModule,
    CoreModule,
    ProfileModule,
    TransactionsModule,
    AccountsModule,
    SystemSettingsModule,
    ReferralModule,
    GatewayModule,
    ReportsModule,
  ],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestMiddleware).forRoutes('*');
  }
}
