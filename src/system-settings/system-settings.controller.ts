import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { SystemSettingsService } from './system-settings.service';
import { CreateSystemSettingDto } from './dto/create-system-setting.dto';
import { UpdateSystemSettingDto } from './dto/update-system-setting.dto';
import { JwtAuthGuard } from 'src/common/guards/jwt.guard';
import { Roles } from 'src/common/decorators/roles.decorator';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { SystemSetting } from './system-setting.entity';

@ApiTags('System Settings')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('system-settings')
export class SystemSettingsController {
  constructor(private readonly systemSettingsService: SystemSettingsService) {}

  @Post()
  @Roles('admin', 'super_admin')
  @ApiOperation({ summary: 'Create new system setting' })
  @ApiResponse({
    status: 201,
    description: 'Setting created successfully',
    type: SystemSetting,
  })
  @ApiResponse({ status: 403, description: 'Forbidden resource' })
  create(@Body() createSystemSettingDto: CreateSystemSettingDto) {
    return this.systemSettingsService.create(createSystemSettingDto);
  }

  @Get()
  @Roles('admin', 'super_admin')
  @ApiOperation({ summary: 'Get all system settings' })
  @ApiResponse({
    status: 200,
    description: 'Returns all system settings',
    type: [SystemSetting],
  })
  @ApiResponse({ status: 403, description: 'Forbidden resource' })
  findAll() {
    return this.systemSettingsService.getAll();
  }

  @Get(':key')
  @Roles('admin', 'super_admin')
  @ApiOperation({ summary: 'Get system setting by key' })
  @ApiResponse({
    status: 200,
    description: 'Returns system setting',
    type: SystemSetting,
  })
  @ApiResponse({ status: 403, description: 'Forbidden resource' })
  @ApiResponse({ status: 404, description: 'Setting not found' })
  findOne(@Param('key') key: string) {
    return this.systemSettingsService.findOne(key);
  }

  @Patch(':key')
  @Roles('admin', 'super_admin')
  @ApiOperation({ summary: 'Update system setting' })
  @ApiResponse({
    status: 200,
    description: 'Setting updated successfully',
    type: SystemSetting,
  })
  @ApiResponse({ status: 403, description: 'Forbidden resource' })
  @ApiResponse({ status: 404, description: 'Setting not found' })
  update(
    @Param('key') key: string,
    @Body() updateSystemSettingDto: UpdateSystemSettingDto,
  ) {
    return this.systemSettingsService.update(key, updateSystemSettingDto);
  }

  @Delete(':key')
  @Roles('admin', 'super_admin')
  @ApiOperation({ summary: 'Delete system setting' })
  @ApiResponse({ status: 200, description: 'Setting deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden resource' })
  @ApiResponse({ status: 404, description: 'Setting not found' })
  remove(@Param('key') key: string) {
    return this.systemSettingsService.remove(key);
  }
}
