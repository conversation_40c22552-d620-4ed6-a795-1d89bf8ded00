/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  InternalServerErrorException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { KYC_VERIFIED_KEY } from '../decorators/kyc.decorator';
import { KycRepository } from '../repository';
import { Request } from 'express';
import { Kyc } from '../schemas';
import { JwtPayload } from '../interfaces';

@Injectable()
export class KycVerificationGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private readonly kycRepo: KycRepository,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requireKycVerification = this.reflector.getAllAndOverride<boolean>(
      KYC_VERIFIED_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!requireKycVerification) {
      return true;
    }

    const request: Request = context.switchToHttp().getRequest();
    const user = request.user as JwtPayload;

    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    try {
      const userKyc = (await this.kycRepo.findByUserId(user.id)) as Kyc;

      if (!userKyc) {
        throw new ForbiddenException(
          'KYC verification required. Please submit your KYC documents to access this resource.',
        );
      }

      const kycRecord = userKyc;

      // Check KYC status
      if (kycRecord.status !== 'approved') {
        const statusMessages = {
          pending:
            'Your KYC verification is pending. Please wait for approval.',
          completed: 'Your KYC is completed. Please wait for approval.',
          rejected: `Your KYC was rejected. Reason: ${kycRecord.rejectionReason || 'Please resubmit your documents.'}`,
        };

        throw new ForbiddenException(
          statusMessages[kycRecord.status] || 'KYC verification required.',
        );
      }

      (request as any).kyc = kycRecord;

      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new InternalServerErrorException('Error checking KYC verification');
    }
  }
}
