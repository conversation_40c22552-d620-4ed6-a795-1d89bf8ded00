/* eslint-disable @typescript-eslint/no-redundant-type-constituents */
import { Injectable, Inject } from '@nestjs/common';
import { count, desc, eq } from 'drizzle-orm';
import { kyc, Kyc, NewKyc } from '../schemas';
import * as schema from '../schemas/kyc';
import { IKycRepository } from '../interfaces/repository/kyc-repository.interface';
import { DATABASE_CONNECTION } from 'src/configs';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { PaginatedResponse, PaginationQuery } from '../interfaces/paginaton';

@Injectable()
export class KycRepository implements IKycRepository {
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}
  async create(kycData: NewKyc): Promise<Kyc> {
    const result = await this.db
      .insert(kyc)
      .values({
        ...kycData,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return result[0];
  }

  async update(id: number, kycData: Partial<Kyc>): Promise<Kyc | null> {
    const result = await this.db
      .update(kyc)
      .set({
        ...kycData,
        updatedAt: new Date(),
      })
      .where(eq(kyc.id, id))
      .returning();

    return result[0] || null;
  }

  async findByUserId(userId: number): Promise<any | null> {
    const result = await this.db.query.kyc.findFirst({
      where: (kyc) => eq(kyc.userId, userId),
      with: { files: true, user: true },
    });

    return result || null;
  }

  async findById(id: number): Promise<Kyc | null> {
    const result = await this.db.query.kyc.findFirst({
      where: (kyc) => eq(kyc.id, id),
      with: { files: true, user: true },
    });

    return result || null;
  }

  async getAll(query: PaginationQuery): Promise<PaginatedResponse<any>> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;

    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(kyc);

    const data = await this.db.query.kyc.findMany({
      limit,
      offset,
      orderBy: (kyc) => [desc(kyc.createdAt)],
      with: { files: true, user: true },
    });

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async delete(id: number): Promise<boolean> {
    const result = await this.db.delete(kyc).where(eq(kyc.id, id)).returning();

    return result.length > 0;
  }
}
