import { Injectable, Inject } from '@nestjs/common';
import { count, eq, desc } from 'drizzle-orm';
import { payments, Payment, transactions } from '../schemas';
import * as schema from '../schemas/payment';
import { IPaymentRepository } from '../interfaces/repository/payment-repistory.interface';
import { DATABASE_CONNECTION } from 'src/configs';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { PaginatedResponse, PaginationQuery } from '../interfaces';

@Injectable()
export class PaymentRepository implements IPaymentRepository {
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}

  async findAll(query: PaginationQuery): Promise<PaginatedResponse<{
    payment: Payment;
    transaction: any;
  }> | null> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;

    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(payments);

    const data = await this.db
      .select({
        payment: payments,
        transaction: transactions,
      })
      .from(payments)
      .leftJoin(transactions, eq(payments.transactionId, transactions.id))
      .limit(limit)
      .offset(offset)
      .orderBy(desc(payments.createdAt));

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async updateByPaymentId(
    paymentId: string,
    paymentData: Partial<Payment>,
  ): Promise<Payment> {
    const result = await this.db
      .update(payments)
      .set({
        ...paymentData,
        updatedAt: new Date(),
      })
      .where(eq(payments.paymentId, paymentId))
      .returning();

    return result[0] || null;
  }

  async create(paymentData: schema.NewPayment): Promise<Payment> {
    const result = await this.db
      .insert(payments)
      .values({ ...paymentData })
      .returning();

    return result[0];
  }

  async findByPaymentId(paymentId: string): Promise<Payment | null> {
    const result = await this.db
      .select()
      .from(payments)
      .where(eq(payments.paymentId, paymentId))
      .limit(1);

    return result[0] || null;
  }

  async findById(id: number): Promise<Payment | null> {
    const result = await this.db
      .select()
      .from(payments)
      .where(eq(payments.id, id))
      .limit(1);

    return result[0] || null;
  }

  async findByTransactionId(transactionId: number): Promise<Payment[] | null> {
    const result = await this.db
      .select()
      .from(payments)
      .where(eq(payments.transactionId, transactionId));

    return result || null;
  }

  async update(id: number, paymentData: Partial<Payment>): Promise<Payment> {
    const result = await this.db
      .update(payments)
      .set({
        ...paymentData,
        updatedAt: new Date(),
      })
      .where(eq(payments.id, id))
      .returning();

    return result[0] || null;
  }

  async delete(id: number): Promise<boolean> {
    const result = await this.db
      .delete(payments)
      .where(eq(payments.id, id))
      .returning();

    return result.length > 0;
  }
}
