/* eslint-disable @typescript-eslint/no-misused-promises */

/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import {
  Injectable,
  Logger,
  OnModuleDestroy,
  OnModuleInit,
} from '@nestjs/common';
import Queue from 'better-queue';
import SqliteStore from 'better-queue-sqlite';
import { join } from 'path';
import { cwd } from 'process';
import { QueuedNotification } from 'src/common/interfaces/queue.interface';

// We no longer need the RawTaskRow interface as we use the queue.get method

@Injectable()
export class NotificationQueueService implements OnModuleInit, OnModuleDestroy {
  private queue: Queue<QueuedNotification>;
  private store: SqliteStore;
  private cleanupInterval: NodeJS.Timeout;

  private logger = new Logger(NotificationQueueService.name);

  onModuleInit() {
    const dbPath = join(cwd(), 'data', 'notifications.db');

    this.store = new SqliteStore({
      path: dbPath,
      tableName: 'notifications_queue',
      // Note: idProperty is not used by SqliteStore, it uses 'id' by default
    });

    this.queue = new Queue(
      (task: QueuedNotification, done) => {
        this.logger.log(
          `Processing notification for userId ${task.userId}: ${task.notification.title}`,
        );
        done(null, task);
      },
      {
        store: this.store,
        concurrent: 1,
        autoResume: true,
      },
    );

    this.logger.log('✅ Notification queue initialized using pure SQLite');

    // This interval will now call a function that respects the public API
    this.cleanupInterval = setInterval(
      () => this.cleanupExpired(),
      1000 * 60 * 60,
    );
  }

  // ... enqueue method remains the same ...
  async enqueue(
    userId: number,
    notification: {
      title: string;
      message: string;
      type: 'info' | 'warning' | 'error' | 'success';
      data?: any;
    },
    expiresInDays = 7,
  ) {
    const taskId = `notif_${Date.now()}_${Math.random().toString(36).slice(2)}`;

    const task: QueuedNotification & { id: string } = {
      id: taskId,
      userId,
      notification: {
        id: taskId,
        ...notification,
        timestamp: new Date().toISOString(),
        read: false,
      },
      expiresAt: Date.now() + expiresInDays * 24 * 60 * 60 * 1000,
    };

    return new Promise<void>((resolve, reject) => {
      this.queue.push(task, (err: Error) => {
        if (err) return reject(err);
        this.logger.log('✅ Notification saved to queue');
        resolve();
      });
    });
  }

  /**
   * CORRECTED: Use direct SQLite query to get pending notifications
   * Since better-queue doesn't have a get() method, we query the store directly
   */
  async getPendingNotifications(
    userId: number,
  ): Promise<QueuedNotification['notification'][]> {
    return new Promise((resolve, reject) => {
      // Access the underlying SQLite database directly
      const db = (this.store as any)._db;
      if (!db) {
        return reject(new Error('Database not initialized'));
      }

      // Query all unlocked tasks from the database
      db.all(
        `SELECT task FROM ${(this.store as any)._tableName} WHERE lock = ?`,
        [''],
        (err: any, rows: any[]) => {
          if (err) return reject(err);

          try {
            const tasks: QueuedNotification[] = rows
              .map((row) => JSON.parse(row.task))
              .filter(
                (task: QueuedNotification) =>
                  task.userId === userId && task.expiresAt > Date.now(),
              );

            const notifications = tasks
              .map((t) => t.notification)
              .sort(
                (a, b) =>
                  new Date(b.timestamp).getTime() -
                  new Date(a.timestamp).getTime(),
              );

            resolve(notifications);
          } catch (parseError) {
            reject(parseError);
          }
        },
      );
    });
  }

  /**
   * This still cannot be implemented with the public API for immediate deletion
   * outside the worker process. The warning remains.
   */
  markAsDelivered(_userId: number, notificationIds: string[]) {
    if (notificationIds.length === 0) return;

    this.logger.warn(
      `🗑 Cannot remove ${notificationIds.length} notifications from queue using public API. 
      Tasks must be processed by the queue worker or expire naturally.`,
    );
  }

  /**
   * The cleanup function now uses direct SQLite query to find and remove expired tasks
   */
  private async cleanupExpired() {
    this.logger.log('Running expired notification cleanup...');

    return new Promise<void>((resolve, reject) => {
      // Access the underlying SQLite database directly
      const db = (this.store as any)._db;
      if (!db) {
        this.logger.error('Database not initialized for cleanup');
        return resolve();
      }

      // Query all unlocked tasks from the database
      db.all(
        `SELECT id, task FROM ${(this.store as any)._tableName} WHERE lock = ?`,
        [''],
        (err: any, rows: any[]) => {
          if (err) {
            this.logger.error('Error querying tasks for cleanup:', err);
            return reject(err);
          }

          try {
            const expiredTaskIds: string[] = [];

            rows.forEach((row: any) => {
              try {
                const task: QueuedNotification = JSON.parse(row.task);
                if (task.expiresAt <= Date.now()) {
                  expiredTaskIds.push(row.id);
                }
              } catch (parseError) {
                this.logger.warn(`Failed to parse task ${row.id}:`, parseError);
              }
            });

            if (expiredTaskIds.length === 0) {
              this.logger.log('No expired notifications found');
              return resolve();
            }

            // Delete expired tasks directly from the database
            const placeholders = expiredTaskIds.map(() => '?').join(',');
            db.run(
              `DELETE FROM ${(this.store as any)._tableName} WHERE id IN (${placeholders})`,
              expiredTaskIds,
              (deleteErr: any) => {
                if (deleteErr) {
                  this.logger.error('Error deleting expired tasks:', deleteErr);
                  return reject(deleteErr);
                }

                this.logger.log(
                  `🧹 Successfully removed ${expiredTaskIds.length} expired notifications`,
                );
                resolve();
              },
            );
          } catch (parseError) {
            this.logger.error('Error processing cleanup:', parseError);
            reject(parseError);
          }
        },
      );
    });
  }

  onModuleDestroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    return new Promise<void>((resolve) =>
      this.queue.destroy(() => {
        // The SqliteStore does not have a public .close() method.
        this.logger.log('⛔ Queue gracefully stopped');
        resolve();
      }),
    );
  }
}
