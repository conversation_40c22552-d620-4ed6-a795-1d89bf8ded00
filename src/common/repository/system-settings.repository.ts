import { Injectable, Inject } from '@nestjs/common';
import { eq } from 'drizzle-orm';
import { systemSettings, SystemSetting } from '../schemas';
import * as schema from '../schemas/system-settings';
import { DATABASE_CONNECTION } from 'src/configs';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { ISystemSettingsRepository } from '../interfaces';

@Injectable()
export class SystemSettingsRepository implements ISystemSettingsRepository {
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}

  async get(key: string): Promise<SystemSetting | null> {
    const result = await this.db
      .select()
      .from(systemSettings)
      .where(eq(systemSettings.key, key))
      .limit(1);

    return result[0] || null;
  }
  async update(key: string, value: string): Promise<SystemSetting | null> {
    const result = await this.db
      .update(systemSettings)
      .set({
        value,
        updatedAt: new Date(),
      })
      .where(eq(systemSettings.key, key))
      .returning();

    return result[0] || null;
  }
  async create(key: string, value: string): Promise<SystemSetting> {
    const result = await this.db
      .insert(systemSettings)
      .values({
        key,
        value,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return result[0];
  }
  async delete(key: string): Promise<boolean> {
    const result = await this.db
      .delete(systemSettings)
      .where(eq(systemSettings.key, key))
      .returning();

    return result.length > 0;
  }
  async getAll(): Promise<SystemSetting[]> {
    const result = await this.db.select().from(systemSettings);

    return result;
  }
}
