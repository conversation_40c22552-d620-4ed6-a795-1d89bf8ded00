import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class UpdatePreferenceDto {
  @ApiProperty({ example: 'dark', description: 'Theme' })
  @IsString()
  @IsOptional()
  theme?: string;
  @ApiProperty({ example: true, description: 'Email notifications' })
  @IsOptional()
  emailNotifications?: boolean;
  @ApiProperty({ example: true, description: 'Push notifications' })
  @IsOptional()
  pushNotifications?: boolean;
  @ApiProperty({ example: true, description: 'SMS notifications' })
  @IsOptional()
  smsNotifications?: boolean;
  @ApiProperty({ example: 'en', description: 'Language' })
  @IsString()
  @IsOptional()
  language?: string;
}
