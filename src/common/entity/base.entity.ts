import { ApiProperty } from '@nestjs/swagger';

export class BaseEntity<T> {
  constructor(data: Partial<BaseEntity<T>>) {
    Object.assign(this, data);
  }

  @ApiProperty({ example: 200 })
  statusCode: number;

  @ApiProperty({ example: 'success' })
  status: string;

  @ApiProperty({ nullable: true })
  data?: T;

  @ApiProperty({ example: 'Request was successful' })
  message?: string;

  static success<T>(
    data: T,
    message = 'Request was successful',
  ): BaseEntity<T> {
    return new BaseEntity<T>({
      statusCode: 200,
      status: 'success',
      data,
      message,
    });
  }

  static error(message: string, statusCode = 500): BaseEntity<null> {
    return new BaseEntity<null>({
      statusCode,
      status: 'error',
      data: null,
      message,
    });
  }
}
