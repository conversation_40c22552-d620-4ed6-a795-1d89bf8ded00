import 'package:equatable/equatable.dart';

/// generate by https://javiercbk.github.io/json_to_dart/
class Autogenerated${upperName} {
  final List<${upperName}Model> results;

  Autogenerated${upperName}({required this.results});

  factory Autogenerated${upperName}.fromJson(Map<String, dynamic> json) {
    var temp = <YouAwesomeModel>[];
    if (json['results'] != null) {
      temp = <${upperName}Model>[];
      json['results'].forEach((v) {
        temp.add(${upperName}Model.fromJson(v as Map<String, dynamic>));
      });
    }
    return Autogenerated${upperName}(results: temp);
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['results'] = results.map((v) => v.toJson()).toList();
    return data;
  }
}

class ${upperName}Model extends Equatable {
  final int id;
  final String name;

  ${upperName}Model(this.id, this.name);

  @override
  List<Object> get props => [id, name];

  factory ${upperName}Model.fromJson(Map<String, dynamic> json) {
    return ${upperName}Model(json['id'] as int, json['name'] as String);
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
  
}
