import { Injectable } from '@nestjs/common';

import { Expo, ExpoPushMessage, ExpoPushTicket } from 'expo-server-sdk';
import { Logger } from '@nestjs/common';

@Injectable()
export class PushNotificationService {
  private expo: Expo;
  private logger = new Logger(PushNotificationService.name);

  constructor() {
    this.expo = new Expo();
  }

  async send({
    pushToken,
    title,
    body,
    data,
  }: {
    pushToken: string;
    title: string;
    body: string;
    data?: Record<string, any>;
  }): Promise<void> {
    try {
      if (!Expo.isExpoPushToken(pushToken)) {
        this.logger.error(`Push token  is not a valid Expo push token`);
        return;
      }

      // Create the message
      const message: ExpoPushMessage = {
        to: pushToken,
        sound: 'default',
        title,
        body,
        data: data || {},
      };

      // Send the notification
      const chunks = this.expo.chunkPushNotifications([message]);
      const tickets: ExpoPushTicket[] = [];

      for (const chunk of chunks) {
        try {
          const ticketChunk = await this.expo.sendPushNotificationsAsync(chunk);
          tickets.push(...ticketChunk);
        } catch (error) {
          this.logger.error('Error sending push notification chunk:', error);
        }
      }

      // Check for errors in tickets
      for (const ticket of tickets) {
        if (ticket.status === 'error') {
          this.logger.error(
            `Error sending notification: ${ticket.message}`,
            ticket.details,
          );
        }
      }
    } catch (error) {
      this.logger.error('Failed to send push notification:', error);
      throw error;
    }
  }

  async sendBulkPushNotifications(
    notifications: Array<{
      pushToken: string;
      title: string;
      body: string;
      data?: Record<string, any>;
    }>,
  ): Promise<void> {
    try {
      const messages: ExpoPushMessage[] = notifications
        .filter((notif) => Expo.isExpoPushToken(notif.pushToken))
        .map((notif) => ({
          to: notif.pushToken,
          sound: 'default',
          title: notif.title,
          body: notif.body,
          data: notif.data || {},
        }));

      const chunks = this.expo.chunkPushNotifications(messages);
      const tickets: ExpoPushTicket[] = [];

      for (const chunk of chunks) {
        try {
          const ticketChunk = await this.expo.sendPushNotificationsAsync(chunk);
          tickets.push(...ticketChunk);
        } catch (error) {
          this.logger.error(
            'Error sending bulk push notification chunk:',
            error,
          );
        }
      }

      // Log any errors
      for (const ticket of tickets) {
        if (ticket.status === 'error') {
          this.logger.error(
            `Error in bulk notification: ${ticket.message}`,
            ticket.details,
          );
        }
      }
    } catch (error) {
      this.logger.error('Failed to send bulk push notifications:', error);
      throw error;
    }
  }
}
