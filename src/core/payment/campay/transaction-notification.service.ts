import { Injectable, Logger } from '@nestjs/common';
import {
  NotificationRepository,
  PaymentRepository,
  TransactionRepository,
  UserRepository,
} from 'src/common/repository';
import { EmailsService } from 'src/core/emails/emails.service';
import { PushNotificationService } from 'src/core/notifications/pushnotification.service';
import { AdminNotificationService } from 'src/core/notifications/admin-notification.service';

@Injectable()
export class TransactionNotificationService {
  private logger = new Logger(TransactionNotificationService.name);
  constructor(
    private readonly notificationRepository: NotificationRepository,
    private readonly userRepository: UserRepository,
    private readonly transactionRepository: TransactionRepository,
    private readonly paymentRepository: PaymentRepository,
    private readonly emailService: EmailsService,
    private readonly pushNotificationService: PushNotificationService,
    private readonly adminNotificationService: AdminNotificationService,
  ) {}
  async successTransactionNotification(id: number) {
    try {
      const transaction = await this.transactionRepository.findById(id);

      if (!transaction?.initiatedBy || !transaction.receivedBy) return;

      const initiator = await this.userRepository.findById(
        transaction.initiatedBy,
      );
      const receiver = await this.userRepository.findById(
        transaction.receivedBy,
      );

      if (!initiator || !receiver) return;

      // Create notifications for both parties
      await this.notificationRepository.create({
        title: 'Transaction Completed',
        message: `Transaction "${transaction.title}" has been completed successfully`,
        userId: initiator.id,
        type: 'personal',
        category: 'transactions',
        priority: 'completed',
        metadata: {
          transactionId: transaction.id,
          amount: transaction.amount,
          direction: 'to_escrow',
          model: 'transaction',
        },
      });

      await this.notificationRepository.create({
        title: 'Transaction Completed',
        message: `Transaction "${transaction.title}" has been completed successfully`,
        userId: receiver.id,
        type: 'personal',
        category: 'transactions',
        priority: 'completed',
        metadata: {
          transactionId: transaction.id,
          amount: transaction.amount,
          direction: 'to_escrow',
          model: 'transaction',
        },
      });

      // Send push notifications to both parties
      if (initiator.notificationToken) {
        await this.pushNotificationService.send({
          title: 'Transaction Completed',
          body: `Transaction "${transaction.title}" has been completed successfully`,
          pushToken: initiator.notificationToken,
          data: {
            transactionId: transaction.id,
          },
        });
      }
      if (receiver.notificationToken) {
        await this.pushNotificationService.send({
          title: 'Transaction Completed',
          body: `Transaction "${transaction.title}" has been completed successfully`,
          pushToken: receiver.notificationToken,
          data: {
            transactionId: transaction.id,
          },
        });
      }

      // Send email notifications to both parties
      // await this.emailService.sendPaymentSuccessEmail(
      //   initiator.email,
      //   initiator.name,
      //   transaction.title,
      //   transaction.amount,
      //   '',
      // );
      // await this.emailService.sendPaymentSuccessEmail(
      //   receiver.email,
      //   receiver.name,
      //   transaction.title,
      //   transaction.amount,
      //   '',
      // );
      // admin notification
      this.adminNotificationService
        .sendNotificationToAdmins({
          title: 'New Transaction Completed',
          message: `A new transaction has been completed by ${initiator.name}`,
          type: 'info',
          data: {
            userId: initiator.id,
            transactionId: transaction.id,
          },
        })
        .catch(() => {
          this.logger.log('Transaction completed notification sent to admins');
        });
    } catch (error) {
      console.error(
        'Failed to send transaction completion notifications:',
        error,
      );
    }
  }

  async activeTransactionNotification(id: number) {
    try {
      const transaction = await this.transactionRepository.findById(id);

      if (!transaction?.initiatedBy || !transaction.receivedBy) return;

      const initiator = await this.userRepository.findById(
        transaction.initiatedBy,
      );
      const receiver = await this.userRepository.findById(
        transaction.receivedBy,
      );

      if (!initiator || !receiver) return;

      // Create notifications for both parties
      await this.notificationRepository.create({
        title: 'Transaction Active',
        message: `Transaction "${transaction.title}" is now active. Payment has been received in escrow.`,
        userId: initiator.id,
        type: 'personal',
        category: 'transactions',
        priority: 'pending',
        metadata: {
          transactionId: transaction.id,
          amount: transaction.amount,
          model: 'transaction',
        },
      });

      await this.notificationRepository.create({
        title: 'Transaction Active',
        message: `Transaction "${transaction.title}" is now active. Payment has been received in escrow.`,
        userId: receiver.id,
        type: 'personal',
        category: 'transactions',
        priority: 'pending',
        metadata: {
          transactionId: transaction.id,
          amount: transaction.amount,
          model: 'transaction',
        },
      });

      // Send push notifications to both parties
      if (initiator.notificationToken) {
        await this.pushNotificationService.send({
          title: 'Transaction Active',
          body: `Transaction "${transaction.title}" is now active. Payment has been received in escrow.`,
          pushToken: initiator.notificationToken,
          data: {
            transactionId: transaction.id,
          },
        });
      }
      if (receiver.notificationToken) {
        await this.pushNotificationService.send({
          title: 'Transaction Active',
          body: `Transaction "${transaction.title}" is now active. Payment has been received in escrow.`,
          pushToken: receiver.notificationToken,
          data: {
            transactionId: transaction.id,
          },
        });
      }

      // Admin notification
      this.adminNotificationService
        .sendNotificationToAdmins({
          title: 'Transaction Activated',
          message: `Transaction "${transaction.title}" is now active with ${transaction.amount} in escrow`,
          type: 'info',
          data: {
            userId: initiator.id,
            transactionId: transaction.id,
            amount: transaction.amount,
          },
        })
        .catch(() => {
          this.logger.log('Transaction active notification sent to admins');
        });
    } catch (error) {
      console.error('Failed to send transaction active notifications:', error);
    }
  }

  async successPaymentNotification(paymentId: number) {
    try {
      const payment = await this.paymentRepository.findById(paymentId);
      if (!payment || !payment.transactionId) return;
      const transaction = await this.transactionRepository.findById(
        payment.transactionId,
      );
      if (!transaction?.initiatedBy || !transaction.receivedBy) return;

      const initiator = await this.userRepository.findById(
        transaction.initiatedBy,
      );
      const receiver = await this.userRepository.findById(
        transaction.receivedBy,
      );
      if (!initiator || !receiver) return;

      const isToEscrow = payment.direction === 'to_escrow';
      const targetUser = isToEscrow ? initiator : receiver;
      const message = isToEscrow
        ? `Payment of ${payment.amount} has been successfully transferred to escrow for transaction "${transaction.title}"`
        : `Payment of ${payment.amount} has been successfully released to you for transaction "${transaction.title}"`;

      // Create notification
      await this.notificationRepository.create({
        title: 'Payment Successful',
        message,
        userId: targetUser.id,
        type: 'personal',
        category: 'transactions',
        priority: 'completed',
        metadata: {
          transactionId: transaction.id,
          paymentId: payment.id,
          amount: payment.amount,
          direction: payment.direction,
          model: 'transaction',
          subModel: 'payment',
        },
      });

      // Send push notification
      if (targetUser.notificationToken) {
        await this.pushNotificationService.send({
          title: 'Payment Successful',
          body: message,
          pushToken: targetUser.notificationToken,
          data: {
            transactionId: transaction.id,
            paymentId: payment.id,
          },
        });
      }

      // Send email notification
      await this.emailService.sendPaymentSuccessEmail(
        targetUser.email,
        targetUser.name,
        transaction.title,
        `${payment.amount}`,
        payment.direction,
      );

      // Admin notification
      this.adminNotificationService
        .sendNotificationToAdmins({
          title: 'Payment Successful',
          message: `Payment of ${payment.amount} ${isToEscrow ? 'to escrow' : 'released'} for transaction "${transaction.title}"`,
          type: 'success',
          data: {
            userId: targetUser.id,
            transactionId: transaction.id,
            paymentId: payment.id,
            direction: payment.direction,
          },
        })
        .catch(() => {
          this.logger.log('Payment success notification sent to admins');
        });
    } catch (error) {
      console.error('Failed to send payment success notifications:', error);
    }
  }
  async failedPaymentNotification(paymentId: number) {
    try {
      const payment = await this.paymentRepository.findById(paymentId);
      if (!payment || !payment.transactionId) return;
      const transaction = await this.transactionRepository.findById(
        payment.transactionId,
      );
      if (!transaction?.initiatedBy || !transaction.receivedBy) return;
      const initiator = await this.userRepository.findById(
        transaction.initiatedBy,
      );
      const receiver = await this.userRepository.findById(
        transaction.receivedBy,
      );
      if (!initiator || !receiver) return;

      const isToEscrow = payment.direction === 'to_escrow';
      const targetUser = isToEscrow ? initiator : receiver;
      const message = isToEscrow
        ? `Payment of ${payment.amount} for transaction "${transaction.title}" has failed`
        : `Payment of ${payment.amount} for transaction "${transaction.title}" has failed`;

      // Create notification
      await this.notificationRepository.create({
        title: 'Payment Failed',
        message,
        userId: targetUser.id,
        type: 'personal',
        category: 'transactions',
        priority: 'error',
        metadata: {
          transactionId: transaction.id,
          paymentId: payment.id,
          amount: payment.amount,
          direction: payment.direction,
          model: 'transaction',
          subModel: 'payment',
        },
      });
      // Send push notification
      if (targetUser.notificationToken) {
        await this.pushNotificationService.send({
          title: 'Payment Failed',
          body: message,
          pushToken: targetUser.notificationToken,
          data: {
            transactionId: transaction.id,
            paymentId: payment.id,
          },
        });
      }

      // Send email notification
      await this.emailService.sendPaymentFailedEmail(
        targetUser.email,
        targetUser.name,
        transaction.title,
        `${payment.amount}`,
        payment.direction,
      );

      // Admin notification
      this.adminNotificationService
        .sendNotificationToAdmins({
          title: 'Payment Failed',
          message: `Payment of ${payment.amount} failed for transaction "${transaction.title}"`,
          type: 'error',
          data: {
            userId: targetUser.id,
            transactionId: transaction.id,
            paymentId: payment.id,
            direction: payment.direction,
          },
        })
        .catch(() => {
          this.logger.log('Payment failed notification sent to admins');
        });
    } catch (error) {
      console.error('Failed to send payment failed notifications:', error);
    }
  }
  async failedTransactionNotification(id: number) {
    try {
      const transaction = await this.transactionRepository.findById(id);

      if (!transaction?.initiatedBy || !transaction.receivedBy) return;

      const initiator = await this.userRepository.findById(
        transaction.initiatedBy,
      );
      const receiver = await this.userRepository.findById(
        transaction.receivedBy,
      );

      if (!initiator || !receiver) return;

      // Create notifications for both parties
      await this.notificationRepository.create({
        title: 'Transaction Failed',
        message: `Transaction "${transaction.title}" has failed`,
        userId: initiator.id,
        type: 'personal',
        category: 'transactions',
        priority: 'error',
        metadata: {
          transactionId: transaction.id,
          model: 'transaction',
        },
      });

      await this.notificationRepository.create({
        title: 'Transaction Failed',
        message: `Transaction "${transaction.title}" has failed`,
        userId: receiver.id,
        type: 'personal',
        category: 'transactions',
        priority: 'error',
        metadata: {
          transactionId: transaction.id,
          model: 'transaction',
        },
      });

      // Send push notifications to both parties
      if (initiator.notificationToken) {
        await this.pushNotificationService.send({
          title: 'Transaction Failed',
          body: `Transaction "${transaction.title}" has failed`,
          pushToken: initiator.notificationToken,
          data: {
            transactionId: transaction.id,
          },
        });
      }
      if (receiver.notificationToken) {
        await this.pushNotificationService.send({
          title: 'Transaction Failed',
          body: `Transaction "${transaction.title}" has failed`,
          pushToken: receiver.notificationToken,
          data: {
            transactionId: transaction.id,
          },
        });
      }

      // Admin notification
      this.adminNotificationService
        .sendNotificationToAdmins({
          title: 'Transaction Failed',
          message: `Transaction "${transaction.title}" has failed for user ${initiator.name}`,
          type: 'error',
          data: {
            userId: initiator.id,
            transactionId: transaction.id,
          },
        })
        .catch(() => {
          this.logger.log('Transaction failed notification sent to admins');
        });
    } catch (error) {
      console.error('Failed to send transaction failure notifications:', error);
    }
  }
  async initiatePaymentNotification(paymentId: number) {
    try {
      const payment = await this.paymentRepository.findById(paymentId);
      if (!payment) return;

      // If no transaction is associated, just notify about payment initiation
      if (!payment.transactionId) {
        console.log('Payment initiated without transaction association');
        return;
      }

      const transaction = await this.transactionRepository.findById(
        payment.transactionId,
      );
      if (!transaction?.initiatedBy || !transaction.receivedBy) return;

      const initiator = await this.userRepository.findById(
        transaction.initiatedBy,
      );
      const receiver = await this.userRepository.findById(
        transaction.receivedBy,
      );
      if (!initiator || !receiver) return;

      const isToEscrow = payment.direction === 'to_escrow';
      const targetUser = isToEscrow ? initiator : receiver;
      const message = isToEscrow
        ? `Payment of ${payment.amount} has been initiated to escrow for transaction "${transaction.title}"`
        : `Payment of ${payment.amount} has been successfully initiated to you for transaction "${transaction.title}"`;

      // Create notification
      await this.notificationRepository.create({
        title: 'Payment Initiated',
        message,
        userId: targetUser.id,
        type: 'personal',
        category: 'transactions',
        priority: 'pending',
        metadata: {
          transactionId: transaction.id,
          paymentId: payment.id,
          amount: payment.amount,
          direction: payment.direction,
          model: 'transaction',
          subModel: 'payment',
        },
      });
      // Send push notification
      if (targetUser.notificationToken) {
        await this.pushNotificationService.send({
          title: 'Payment Initiated',
          body: message,
          pushToken: targetUser.notificationToken,
          data: {
            transactionId: transaction.id,
            paymentId: payment.id,
          },
        });
      }
    } catch (error) {
      console.error('Failed to send payment initiated notifications:', error);
    }
  }

  async successDisbursementNotification(paymentId: number) {
    try {
      const payment = await this.paymentRepository.findById(paymentId);
      if (!payment || !payment.transactionId) return;
      const transaction = await this.transactionRepository.findById(
        payment.transactionId,
      );
      if (!transaction?.initiatedBy || !transaction.receivedBy) return;

      const initiator = await this.userRepository.findById(
        transaction.initiatedBy,
      );
      const receiver = await this.userRepository.findById(
        transaction.receivedBy,
      );
      if (!initiator || !receiver) return;

      const targetUser = receiver;
      const message = `Disbursement of ${payment.amount} has been successfully released to you for transaction "${transaction.title}"`;

      // Create notification
      await this.notificationRepository.create({
        title: 'Disbursement Successful',
        message,
        userId: targetUser.id,
        type: 'personal',
        category: 'transactions',
        priority: 'completed',
        metadata: {
          transactionId: transaction.id,
          paymentId: payment.id,
          amount: payment.amount,
          direction: payment.direction,
          model: 'transaction',
          subModel: 'payment',
        },
      });

      // Send push notification
      if (targetUser.notificationToken) {
        await this.pushNotificationService.send({
          title: 'Disbursement Successful',
          body: message,
          pushToken: targetUser.notificationToken,
          data: {
            transactionId: transaction.id,
            paymentId: payment.id,
          },
        });
      }

      // Send email notification
      await this.emailService.sendPaymentSuccessEmail(
        targetUser.email,
        targetUser.name,
        transaction.title,
        `${payment.amount}`,
        payment.direction,
      );

      // Admin notification
      this.adminNotificationService
        .sendNotificationToAdmins({
          title: 'Disbursement Successful',
          message: `Disbursement of ${payment.amount} released to ${receiver.name} for transaction "${transaction.title}"`,
          type: 'success',
          data: {
            userId: receiver.id,
            transactionId: transaction.id,
            paymentId: payment.id,
          },
        })
        .catch(() => {
          this.logger.log('Disbursement success notification sent to admins');
        });
    } catch (error) {
      console.error(
        'Failed to send disbursement success notifications:',
        error,
      );
    }
  }

  async failedDisbursementNotification(paymentId: number) {
    try {
      const payment = await this.paymentRepository.findById(paymentId);
      if (!payment || !payment.transactionId) return;
      const transaction = await this.transactionRepository.findById(
        payment.transactionId,
      );
      if (!transaction?.initiatedBy || !transaction.receivedBy) return;
      const initiator = await this.userRepository.findById(
        transaction.initiatedBy,
      );
      const receiver = await this.userRepository.findById(
        transaction.receivedBy,
      );
      if (!initiator || !receiver) return;

      const targetUser = receiver;
      const message = `Disbursement of ${payment.amount} for transaction "${transaction.title}" has failed`;

      // Create notification
      await this.notificationRepository.create({
        title: 'Disbursement Failed',
        message,
        userId: targetUser.id,
        type: 'personal',
        category: 'transactions',
        priority: 'error',
        metadata: {
          transactionId: transaction.id,
          paymentId: payment.id,
          amount: payment.amount,
          direction: payment.direction,
          model: 'transaction',
          subModel: 'payment',
        },
      });
      // Send push notification
      if (targetUser.notificationToken) {
        await this.pushNotificationService.send({
          title: 'Disbursement Failed',
          body: message,
          pushToken: targetUser.notificationToken,
          data: {
            transactionId: transaction.id,
            paymentId: payment.id,
          },
        });
      }

      // Send email notification
      await this.emailService.sendPaymentFailedEmail(
        targetUser.email,
        targetUser.name,
        transaction.title,
        `${payment.amount}`,
        payment.direction,
      );

      // Admin notification
      this.adminNotificationService
        .sendNotificationToAdmins({
          title: 'Disbursement Failed',
          message: `Disbursement of ${payment.amount} failed for transaction "${transaction.title}"`,
          type: 'error',
          data: {
            userId: receiver.id,
            transactionId: transaction.id,
            paymentId: payment.id,
          },
        })
        .catch(() => {
          this.logger.log('Disbursement failed notification sent to admins');
        });
    } catch (error) {
      console.error('Failed to send disbursement failed notifications:', error);
    }
  }

  async initiateDisbursementNotification(paymentId: number) {
    try {
      const payment = await this.paymentRepository.findById(paymentId);
      if (!payment || !payment.transactionId) return;
      const transaction = await this.transactionRepository.findById(
        payment.transactionId,
      );
      if (!transaction?.initiatedBy || !transaction.receivedBy) return;

      const initiator = await this.userRepository.findById(
        transaction.initiatedBy,
      );
      const receiver = await this.userRepository.findById(
        transaction.receivedBy,
      );
      if (!initiator || !receiver) return;

      const targetUser = receiver;
      const message = `Disbursement of ${payment.amount} has been initiated to you for transaction "${transaction.title}"`;

      // Create notification
      await this.notificationRepository.create({
        title: 'Disbursement Initiated',
        message,
        userId: targetUser.id,
        type: 'personal',
        category: 'transactions',
        priority: 'pending',
        metadata: {
          transactionId: transaction.id,
          paymentId: payment.id,
          amount: payment.amount,
          direction: payment.direction,
          model: 'transaction',
          subModel: 'payment',
        },
      });
      // Send push notification
      if (targetUser.notificationToken) {
        await this.pushNotificationService.send({
          title: 'Disbursement Initiated',
          body: message,
          pushToken: targetUser.notificationToken,
          data: {
            transactionId: transaction.id,
            paymentId: payment.id,
          },
        });
      }
    } catch (error) {
      console.error(
        'Failed to send disbursement initiated notifications:',
        error,
      );
    }
  }

  async successRefundNotification(paymentId: number) {
    try {
      const payment = await this.paymentRepository.findById(paymentId);
      if (!payment || !payment.transactionId) return;
      const transaction = await this.transactionRepository.findById(
        payment.transactionId,
      );
      if (!transaction?.initiatedBy || !transaction.receivedBy) return;

      const initiator = await this.userRepository.findById(
        transaction.initiatedBy,
      );
      const receiver = await this.userRepository.findById(
        transaction.receivedBy,
      );
      if (!initiator || !receiver) return;

      const targetUser = initiator;
      const message = `Refund of ${payment.amount} has been successfully processed for declined transaction "${transaction.title}"`;

      // Create notification
      await this.notificationRepository.create({
        title: 'Refund Successful',
        message,
        userId: targetUser.id,
        type: 'personal',
        category: 'transactions',
        priority: 'completed',
        metadata: {
          transactionId: transaction.id,
          paymentId: payment.id,
          amount: payment.amount,
          direction: payment.direction,
          model: 'transaction',
          subModel: 'payment',
        },
      });

      // Send push notification
      if (targetUser.notificationToken) {
        await this.pushNotificationService.send({
          title: 'Refund Successful',
          body: message,
          pushToken: targetUser.notificationToken,
          data: {
            transactionId: transaction.id,
            paymentId: payment.id,
          },
        });
      }

      // Send email notification
      await this.emailService.sendPaymentSuccessEmail(
        targetUser.email,
        targetUser.name,
        transaction.title,
        `${payment.amount}`,
        'refund',
      );

      // Admin notification
      this.adminNotificationService
        .sendNotificationToAdmins({
          title: 'Refund Successful',
          message: `Refund of ${payment.amount} processed for ${initiator.name} for declined transaction "${transaction.title}"`,
          type: 'success',
          data: {
            userId: initiator.id,
            transactionId: transaction.id,
            paymentId: payment.id,
          },
        })
        .catch(() => {
          this.logger.log('Refund success notification sent to admins');
        });
    } catch (error) {
      console.error('Failed to send refund success notifications:', error);
    }
  }

  async failedRefundNotification(paymentId: number) {
    try {
      const payment = await this.paymentRepository.findById(paymentId);
      if (!payment || !payment.transactionId) return;
      const transaction = await this.transactionRepository.findById(
        payment.transactionId,
      );
      if (!transaction?.initiatedBy || !transaction.receivedBy) return;

      const initiator = await this.userRepository.findById(
        transaction.initiatedBy,
      );
      const receiver = await this.userRepository.findById(
        transaction.receivedBy,
      );
      if (!initiator || !receiver) return;

      const targetUser = initiator;
      const message = `Refund of ${payment.amount} for declined transaction "${transaction.title}" has failed`;

      // Create notification
      await this.notificationRepository.create({
        title: 'Refund Failed',
        message,
        userId: targetUser.id,
        type: 'personal',
        category: 'transactions',
        priority: 'error',
        metadata: {
          transactionId: transaction.id,
          paymentId: payment.id,
          amount: payment.amount,
          direction: payment.direction,
          model: 'transaction',
          subModel: 'payment',
        },
      });

      // Send push notification
      if (targetUser.notificationToken) {
        await this.pushNotificationService.send({
          title: 'Refund Failed',
          body: message,
          pushToken: targetUser.notificationToken,
          data: {
            transactionId: transaction.id,
            paymentId: payment.id,
          },
        });
      }

      // Send email notification
      await this.emailService.sendPaymentFailedEmail(
        targetUser.email,
        targetUser.name,
        transaction.title,
        `${payment.amount}`,
        'refund',
      );

      // Admin notification
      this.adminNotificationService
        .sendNotificationToAdmins({
          title: 'Refund Failed',
          message: `Refund of ${payment.amount} failed for ${initiator.name} for declined transaction "${transaction.title}"`,
          type: 'error',
          data: {
            userId: initiator.id,
            transactionId: transaction.id,
            paymentId: payment.id,
          },
        })
        .catch(() => {
          this.logger.log('Refund failed notification sent to admins');
        });
    } catch (error) {
      console.error('Failed to send refund failed notifications:', error);
    }
  }
}
