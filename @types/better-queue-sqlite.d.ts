declare module 'better-queue-sqlite' {
  interface SqliteStoreOptions {
    path?: string;
    tableName?: string;
  }

  interface SqliteDatabase {
    all(
      sql: string,
      params: any[],
      callback: (err: any, rows: any[]) => void,
    ): void;
    run(sql: string, params: any[], callback?: (err: any) => void): void;
    close(callback?: (err: any) => void): void;
  }

  class SqliteStore {
    _db: SqliteDatabase;
    _tableName: string;

    constructor(options?: SqliteStoreOptions);
    connect(callback: (err: any, count?: number) => void): void;
    getTask(taskId: string, callback: (err: any, task?: any) => void): void;
    deleteTask(taskId: string, callback: (err: any) => void): void;
    putTask(
      taskId: string,
      task: any,
      priority: number,
      callback: (err: any) => void,
    ): void;
    getLock(lockId: string, callback: (err: any, tasks?: any) => void): void;
    getRunningTasks(callback: (err: any, tasks?: any) => void): void;
    releaseLock(lockId: string, callback: (err: any) => void): void;
    takeFirstN(
      num: number,
      callback: (err: any, lockId?: string) => void,
    ): void;
    takeLastN(num: number, callback: (err: any, lockId?: string) => void): void;
    close(callback?: (err: any) => void): void;
  }

  export = SqliteStore;
}
