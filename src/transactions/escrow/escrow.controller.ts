import {
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Query,
  UseGuards,
} from '@nestjs/common';
import { EscrowService } from './escrow.service';
import { PaginationQueryDto } from 'src/common/dto/pagination.query.do';
import { JwtAuthGuard } from 'src/common/guards/jwt.guard';
import { RolesGuard } from 'src/common/guards/roles.guard';
import { Roles } from 'src/common/decorators/roles.decorator';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';

@ApiTags('Escrow')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('escrow')
export class EscrowController {
  constructor(private readonly escrowService: EscrowService) {}

  @Get(':id')
  @ApiOperation({ summary: 'Get escrow by ID' })
  @ApiResponse({ status: 200, description: 'Returns the escrow record' })
  @ApiResponse({ status: 404, description: 'Escrow not found' })
  @Roles('admin', 'super_admin')
  async getById(@Param('id', ParseIntPipe) id: number) {
    return this.escrowService.getById(id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all escrow records' })
  @ApiResponse({ status: 200, description: 'Returns paginated escrow records' })
  @Roles('admin', 'super_admin')
  async getAll(@Query() query: PaginationQueryDto) {
    return this.escrowService.getAll(query);
  }

  @Get('fee/:amount')
  @ApiOperation({ summary: 'Get transaction fee' })
  @ApiResponse({
    status: 200,
    description: 'Returns the calculated transaction fee',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async getTransactionFee(@Param('amount', ParseIntPipe) amount: number) {
    return this.escrowService.getTranactionFees(amount);
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get escrow stats' })
  @ApiResponse({ status: 200, description: 'Returns escrow stats' })
  @Roles('admin')
  async getStats() {
    return this.escrowService.getStats();
  }
  @Get('income')
  @ApiOperation({ summary: 'Get escrow income' })
  @ApiResponse({ status: 200, description: 'Returns escrow income' })
  @Roles('admin')
  async getIncome() {
    return this.escrowService.getIncome();
  }
  @Get('transaction/:id')
  @ApiOperation({ summary: 'Get escrow by transaction ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns the escrow record for the given transaction',
  })
  @ApiResponse({ status: 404, description: 'Escrow not found' })
  @Roles('admin')
  async getByTransactionId(@Param('id', ParseIntPipe) id: number) {
    return this.escrowService.getByTransactionId(id);
  }
}
