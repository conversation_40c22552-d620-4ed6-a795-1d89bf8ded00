import { Module } from '@nestjs/common';
import { SystemSettingsService } from './system-settings.service';
import { SystemSettingsController } from './system-settings.controller';
import { SystemSettingsRepository } from 'src/common/repository/system-settings.repository';

@Module({
  controllers: [SystemSettingsController],
  providers: [SystemSettingsService, SystemSettingsRepository],
})
export class SystemSettingsModule {}
