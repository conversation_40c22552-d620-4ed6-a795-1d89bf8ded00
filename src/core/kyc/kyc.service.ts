/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { EmailsService } from '../emails/emails.service';
import { FilesService } from '../files/files.service';
import {
  FileRepository,
  KycRepository,
  UserRepository,
} from './../../common/repository';
import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { CreateKycDto } from './dto/create.dto';
import { PaginationQuery } from 'src/common/interfaces';
import { NotificationsService } from '../notifications/notifications.service';
import { DATABASE_CONNECTION } from 'src/configs';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import * as schema from 'src/common/schemas';
import { eq } from 'drizzle-orm';
import { KycNotificationService } from './kyc-notification.service';
import { AdminNotificationService } from '../notifications/admin-notification.service';

@Injectable()
export class KycService {
  private logger = new Logger(KycService.name);
  constructor(
    private readonly kycRepository: KycRepository,
    private readonly emailService: EmailsService,
    private readonly fileService: FilesService,
    private readonly usersRepository: UserRepository,
    private readonly fileRepository: FileRepository,
    private readonly adminNotificationService: AdminNotificationService,
    private readonly notificationsService: NotificationsService,
    private readonly kycNotificationService: KycNotificationService,
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}

  async getKycById(id: number) {
    try {
      const kyc = await this.kycRepository.findById(id);
      if (!kyc) throw new BadRequestException('KYC not found');
      return kyc;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async create(
    dto: CreateKycDto,
    userId: number,
    files?: {
      frontId?: Express.Multer.File[];
      backId?: Express.Multer.File[];
      selfie?: Express.Multer.File[];
    },
  ) {
    try {
      let shouldSendVerification = false;
      let userPhone: string | null = null;

      const user = await this.usersRepository.findById(userId);

      if (!user) throw new BadRequestException('User not found');

      const kyc = await this.db.transaction(async (tx) => {
        const user = await tx.query.users.findFirst({
          where: (users) => eq(users.id, userId),
        });
        if (!user) throw new BadRequestException('User not found');

        const existingKyc = await tx.query.kyc.findFirst({
          where: (kyc) => eq(kyc.userId, userId),
          with: {
            files: true,
          },
        });

        let kycRecord: schema.Kyc;

        if (existingKyc) {
          // Update existing KYC
          if (dto.phoneNumber) {
            const existingUserWithPhone = await tx.query.users.findFirst({
              where: (users) => eq(users.phone, dto.phoneNumber!),
            });
            if (existingUserWithPhone) {
              if (!existingUserWithPhone.isPhoneVerified) {
                shouldSendVerification = true;
                userPhone = dto.phoneNumber;
              }
            } else {
              await tx
                .update(schema.users)
                .set({
                  phone: dto.phoneNumber,
                  isPhoneVerified: false,
                })
                .where(eq(schema.users.id, userId));
              shouldSendVerification = true;
              userPhone = dto.phoneNumber;
            }
          }

          // Delete old files from Cloudinary
          if (existingKyc.files && existingKyc.files.length > 0) {
            for (const file of existingKyc.files) {
              try {
                await this.fileService.deleteFile(file.url!);
              } catch (error) {
                // Log error but continue with update
                console.error(
                  `Failed to delete file from Cloudinary: ${file.url}`,
                  error,
                );
              }
            }
            // Delete file records from database
            await tx
              .delete(schema.files)
              .where(eq(schema.files.kycId, existingKyc.id));
          }

          // Update KYC record
          const [updatedKyc] = await tx
            .update(schema.kyc)
            .set({
              country: dto.country,
              dob: new Date(dto.dob as unknown as string),
              address: dto.address,
              status: 'pending',
            })
            .where(eq(schema.kyc.id, existingKyc.id))
            .returning();

          kycRecord = updatedKyc;
        } else {
          // Create new KYC
          if (dto.phoneNumber) {
            const existingUserWithPhone = await tx.query.users.findFirst({
              where: (users) => eq(users.phone, dto.phoneNumber!),
            });
            if (existingUserWithPhone) {
              if (!existingUserWithPhone.isPhoneVerified) {
                shouldSendVerification = true;
                userPhone = dto.phoneNumber;
              }
            } else {
              await tx
                .update(schema.users)
                .set({
                  phone: dto.phoneNumber,
                  isPhoneVerified: false,
                })
                .where(eq(schema.users.id, userId));
              shouldSendVerification = true;
              userPhone = dto.phoneNumber;
            }
          }

          const [newKyc] = await tx
            .insert(schema.kyc)
            .values({
              userId: userId,
              country: dto.country,
              dob: new Date(dto.dob as unknown as string),
              address: dto.address,
              status: 'pending',
            })
            .returning();

          kycRecord = newKyc;
        }

        // Track uploaded files
        const uploadedFiles = {
          frontId: false,
          backId: false,
          selfie: false,
        };

        if (files) {
          if (files.frontId) {
            const frontIdUpload = await this.fileService.uploadFile(
              files.frontId[0],
            );
            await tx.insert(schema.files).values({
              name: 'frontId',
              url: frontIdUpload.url,
              kycId: kycRecord.id,
            });
            uploadedFiles.frontId = true;
          }
          if (files.backId) {
            const backIdUpload = await this.fileService.uploadFile(
              files.backId[0],
            );
            await tx.insert(schema.files).values({
              name: 'backId',
              url: backIdUpload.url,
              kycId: kycRecord.id,
            });
            uploadedFiles.backId = true;
          }
          if (files.selfie) {
            const selfieUpload = await this.fileService.uploadFile(
              files.selfie[0],
            );
            await tx.insert(schema.files).values({
              name: 'selfie',
              url: selfieUpload.url,
              kycId: kycRecord.id,
            });
            uploadedFiles.selfie = true;
          }
        }

        // Check if all required files are uploaded
        const allFilesUploaded =
          uploadedFiles.frontId && uploadedFiles.backId && uploadedFiles.selfie;

        // Update KYC status to completed if all files are present
        if (allFilesUploaded) {
          const [updatedKyc] = await tx
            .update(schema.kyc)
            .set({ status: 'completed' })
            .where(eq(schema.kyc.id, kycRecord.id))
            .returning();

          return updatedKyc;
        }

        return kycRecord;
      });

      // Send notifications after transaction
      if (shouldSendVerification && userPhone) {
        // try {
        //   await this.smsService.sendVerificationCode(userPhone);
        // } catch (error) {
        //   this.logger.error('Failed to send verification SMS', error);
        // }
      }

      // Send notifications if KYC is completed
      if (kyc.status === 'completed') {
        this.kycNotificationService
          .notifyKycCompleted(userId, kyc.id, user.name, user.email)
          .catch(() => {
            this.logger.log('KYC completed notification sent');
          });
        this.adminNotificationService
          .sendNotificationToAdmins({
            title: 'New KYC Completed',
            message: `A new KYC has been completed by ${user.name}`,
            type: 'info',
            data: {
              userId: userId,
              kycId: kyc.id,
            },
          })
          .catch(() => {
            this.logger.log('KYC completed notification sent to admins');
          });
        // try {
        //   await this.emailService.sendKycCompletedEmail(userId);
        // } catch (error) {
        //   this.logger.error('Failed to send KYC completion email', error);
        // }
      }

      return kyc;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getAll(query: PaginationQuery) {
    try {
      return await this.kycRepository.getAll(query);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async findByUserId(userId: number) {
    try {
      const kyc = await this.kycRepository.findByUserId(userId);
      if (!kyc) return null;
      return kyc;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async approveKyc(id: number) {
    try {
      const user = await this.usersRepository.findById(id);
      if (!user) throw new BadRequestException('user not found');

      const kyc = await this.kycRepository.findById(id);
      if (!kyc) throw new BadRequestException('KYC not found');
      const updatedKyc = await this.kycRepository.update(id, {
        status: 'approved',
      });

      if (updatedKyc) {
        this.kycNotificationService
          .notifyKycApproved(id, updatedKyc.id, user.name, user.email)
          .catch(() => {
            this.logger.log('KYC approved notification sent');
          });
        this.adminNotificationService
          .sendNotificationToAdmins({
            title: 'KYC Approved',
            message: `KYC for ${user.name} has been approved`,
            type: 'info',
            data: {
              userId: id,
              kycId: kyc.id,
            },
          })
          .catch(() => {
            this.logger.log('KYC approved notification sent to admins');
          });
      }

      return updatedKyc;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async rejectKyc(id: number, reason: string) {
    try {
      const user = await this.usersRepository.findById(id);
      if (!user) throw new BadRequestException('user not found');

      const kyc = await this.kycRepository.findById(id);
      if (!kyc) throw new BadRequestException('KYC not found');
      const updatedKyc = await this.kycRepository.update(id, {
        status: 'rejected',
      });

      if (updatedKyc) {
        this.kycNotificationService
          .notifyKycRejected(id, updatedKyc.id, user.name, user.email, reason)
          .catch(() => {
            this.logger.log('KYC rejected notification sent');
          });
        this.adminNotificationService
          .sendNotificationToAdmins({
            title: 'KYC Rejected',
            message: `KYC for ${user.name} has been rejected`,
            type: 'info',
            data: {
              userId: id,
              kycId: kyc.id,
            },
          })
          .catch(() => {
            this.logger.log('KYC rejected notification sent to admins');
          });
        // await this.emailService.sendCustomEmail(
        //   user.email,
        //   user.name,
        //   'Kyc Status',
        //   `Your kyc has been rejected because ${reason}`,
        // );
      }

      return kyc;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async delete(id: number) {
    try {
      const kyc = await this.kycRepository.findById(id);
      if (!kyc) throw new BadRequestException('KYC not found');
      const deleted = await this.kycRepository.delete(id);
      if (!deleted) throw new BadRequestException('Failed to delete KYC');
      return { message: 'KYC deleted successfully' };
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
}
