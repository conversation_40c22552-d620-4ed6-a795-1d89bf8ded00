import { Module } from '@nestjs/common';
import { ReferralController } from './referral.controller';
import { ReferralService } from './referral.service';
import { ReferalRepository } from 'src/common/repository/referal.repository';
import { UserRepository } from 'src/common/repository';

@Module({
  controllers: [ReferralController],
  providers: [ReferralService, ReferalRepository, UserRepository],
})
export class ReferralModule {}
