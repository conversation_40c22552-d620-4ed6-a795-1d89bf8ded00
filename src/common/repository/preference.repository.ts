import { Injectable, Inject } from '@nestjs/common';
import { eq } from 'drizzle-orm';
import { preferences, Preference } from '../schemas';
import * as schema from '../schemas/preference';
import { IPreferenceRepository } from '../interfaces/repository/preference-interface.repository';
import { DATABASE_CONNECTION } from 'src/configs';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

@Injectable()
export class PreferenceRepository implements IPreferenceRepository {
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}

  async create(userId: number): Promise<Preference> {
    const result = await this.db
      .insert(preferences)
      .values({
        userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return result[0];
  }

  async update(
    userId: number,
    preferenceData: Partial<Preference>,
  ): Promise<Preference | null> {
    const result = await this.db
      .update(preferences)
      .set({
        ...preferenceData,
        updatedAt: new Date(),
      })
      .where(eq(preferences.userId, userId))
      .returning();

    return result[0] || null;
  }

  async findByUserId(userId: number): Promise<Preference | null> {
    const result = await this.db
      .select()
      .from(preferences)
      .where(eq(preferences.userId, userId))
      .limit(1);

    return result[0] || null;
  }
}
