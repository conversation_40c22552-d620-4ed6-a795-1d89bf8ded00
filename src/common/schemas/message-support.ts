// import {
//   pgTable,
//   serial,
//   varchar,
//   timestamp,
//   text,
//   integer,
//   boolean,
// } from 'drizzle-orm/pg-core';
// import { users } from './users';
// import { relations } from 'drizzle-orm';

// export const messageSupportTickets = pgTable('message_support_tickets', {
//   id: serial('id').primaryKey(),
//   userId: integer('user_id')
//     .notNull()
//     .references(() => users.id, { onDelete: 'cascade' }),
//   subject: varchar('subject', { length: 255 }).notNull(),
//   status: varchar('status', { length: 50 }).notNull().default('open'),
//   priority: varchar('priority', { length: 50 }).notNull().default('medium'),
//   createdAt: timestamp('created_at').defaultNow().notNull(),
//   updatedAt: timestamp('updated_at').defaultNow().notNull(),
//   closedAt: timestamp('closed_at'),
// });

// export const messageSupportMessages = pgTable('message_support_messages', {
//   id: serial('id').primaryKey(),
//   ticketId: integer('ticket_id')
//     .notNull()
//     .references(() => messageSupportTickets.id, { onDelete: 'cascade' }),
//   senderId: integer('sender_id')
//     .notNull()
//     .references(() => users.id, { onDelete: 'cascade' }),
//   message: text('message').notNull(),
//   isAdminMessage: boolean('is_admin_message').notNull().default(false),
//   isRead: boolean('is_read').notNull().default(false),
//   createdAt: timestamp('created_at').defaultNow().notNull(),
// });

// export const messageSupportTicketsRelations = relations(
//   messageSupportTickets,
//   ({ one, many }) => ({
//     user: one(users, {
//       fields: [messageSupportTickets.userId],
//       references: [users.id],
//     }),
//     messages: many(messageSupportMessages),
//   }),
// );

// export const messageSupportMessagesRelations = relations(
//   messageSupportMessages,
//   ({ one }) => ({
//     ticket: one(messageSupportTickets, {
//       fields: [messageSupportMessages.ticketId],
//       references: [messageSupportTickets.id],
//     }),
//     sender: one(users, {
//       fields: [messageSupportMessages.senderId],
//       references: [users.id],
//     }),
//   }),
// );

// export type MessageSupportTicket = typeof messageSupportTickets.$inferSelect;
// export type NewMessageSupportTicket = typeof messageSupportTickets.$inferInsert;
// export type MessageSupportMessage = typeof messageSupportMessages.$inferSelect;
// export type NewMessageSupportMessage =
//   typeof messageSupportMessages.$inferInsert;
