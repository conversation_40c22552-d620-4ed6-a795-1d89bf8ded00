{"id": "96d2f372-7808-4dd4-92ec-7216f2b2edb7", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.api-keys": {"name": "api-keys", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "api-key": {"name": "api-key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "api-secret": {"name": "api-secret", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.devices": {"name": "devices", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "ip": {"name": "ip", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "browser": {"name": "browser", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"devices_user_id_users_id_fk": {"name": "devices_user_id_users_id_fk", "tableFrom": "devices", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.escrow": {"name": "escrow", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "amount": {"name": "amount", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "escrow_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "fee": {"name": "fee", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "dead_line": {"name": "dead_line", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "comments": {"name": "comments", "type": "<PERSON><PERSON><PERSON>(255)[]", "primaryKey": false, "notNull": false}, "transaction_id": {"name": "transaction_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"escrow_transaction_id_transactions_id_fk": {"name": "escrow_transaction_id_transactions_id_fk", "tableFrom": "escrow", "tableTo": "transactions", "columnsFrom": ["transaction_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.files": {"name": "files", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "kyc_id": {"name": "kyc_id", "type": "integer", "primaryKey": false, "notNull": false}, "transaction_id": {"name": "transaction_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"files_kyc_id_kyc_id_fk": {"name": "files_kyc_id_kyc_id_fk", "tableFrom": "files", "tableTo": "kyc", "columnsFrom": ["kyc_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "files_transaction_id_transactions_id_fk": {"name": "files_transaction_id_transactions_id_fk", "tableFrom": "files", "tableTo": "transactions", "columnsFrom": ["transaction_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.kyc": {"name": "kyc", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "dob": {"name": "dob", "type": "timestamp", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "kyc_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"kyc_user_id_users_id_fk": {"name": "kyc_user_id_users_id_fk", "tableFrom": "kyc", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.profiles": {"name": "profiles", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "avatar": {"name": "avatar", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"profiles_user_id_users_id_fk": {"name": "profiles_user_id_users_id_fk", "tableFrom": "profiles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"profiles_code_unique": {"name": "profiles_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "role", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'user'"}, "referral_code": {"name": "referral_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "google_id": {"name": "google_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "is_email_verified": {"name": "is_email_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_phone_verified": {"name": "is_phone_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_banned": {"name": "is_banned", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "banned_at": {"name": "banned_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "notification_token": {"name": "notification_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "verified_at": {"name": "verified_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "users_phone_unique": {"name": "users_phone_unique", "nullsNotDistinct": false, "columns": ["phone"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.transactions": {"name": "transactions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "transaction_id": {"name": "transaction_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "aggrement": {"name": "aggrement", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "dead_line": {"name": "dead_line", "type": "timestamp", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "received_by": {"name": "received_by", "type": "integer", "primaryKey": false, "notNull": false}, "payment_method_id": {"name": "payment_method_id", "type": "integer", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"transactions_user_id_users_id_fk": {"name": "transactions_user_id_users_id_fk", "tableFrom": "transactions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "transactions_received_by_users_id_fk": {"name": "transactions_received_by_users_id_fk", "tableFrom": "transactions", "tableTo": "users", "columnsFrom": ["received_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "transactions_payment_method_id_payment_methods_id_fk": {"name": "transactions_payment_method_id_payment_methods_id_fk", "tableFrom": "transactions", "tableTo": "payment_methods", "columnsFrom": ["payment_method_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"transactions_transaction_id_unique": {"name": "transactions_transaction_id_unique", "nullsNotDistinct": false, "columns": ["transaction_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment_methods": {"name": "payment_methods", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "payment_method_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "expire_date": {"name": "expire_date", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "cvv": {"name": "cvv", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "account_number": {"name": "account_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "country_code": {"name": "country_code", "type": "text", "primaryKey": false, "notNull": false}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "account_name": {"name": "account_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"payment_methods_user_id_users_id_fk": {"name": "payment_methods_user_id_users_id_fk", "tableFrom": "payment_methods", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"payment_methods_account_number_unique": {"name": "payment_methods_account_number_unique", "nullsNotDistinct": false, "columns": ["account_number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payments": {"name": "payments", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "payment_id": {"name": "payment_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "payment_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "direction": {"name": "direction", "type": "payment_direction", "typeSchema": "public", "primaryKey": false, "notNull": true}, "transaction_id": {"name": "transaction_id", "type": "integer", "primaryKey": false, "notNull": false}, "payment_method_id": {"name": "payment_method_id", "type": "integer", "primaryKey": false, "notNull": false}, "reference": {"name": "reference", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"payments_transaction_id_transactions_id_fk": {"name": "payments_transaction_id_transactions_id_fk", "tableFrom": "payments", "tableTo": "transactions", "columnsFrom": ["transaction_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "payments_payment_method_id_payment_methods_id_fk": {"name": "payments_payment_method_id_payment_methods_id_fk", "tableFrom": "payments", "tableTo": "payment_methods", "columnsFrom": ["payment_method_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"payments_payment_id_unique": {"name": "payments_payment_id_unique", "nullsNotDistinct": false, "columns": ["payment_id"]}, "payments_reference_unique": {"name": "payments_reference_unique", "nullsNotDistinct": false, "columns": ["reference"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "action_required": {"name": "action_required", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "priority", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'info'"}, "category": {"name": "category", "type": "category", "typeSchema": "public", "primaryKey": false, "notNull": false}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_archived": {"name": "is_archived", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"notifications_user_id_users_id_fk": {"name": "notifications_user_id_users_id_fk", "tableFrom": "notifications", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tokens": {"name": "tokens", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"tokens_user_id_users_id_fk": {"name": "tokens_user_id_users_id_fk", "tableFrom": "tokens", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"tokens_token_unique": {"name": "tokens_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.preferences": {"name": "preferences", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "theme": {"name": "theme", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email_notifications": {"name": "email_notifications", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "push_notifications": {"name": "push_notifications", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "sms_notifications": {"name": "sms_notifications", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"preferences_user_id_users_id_fk": {"name": "preferences_user_id_users_id_fk", "tableFrom": "preferences", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.system_settings": {"name": "system_settings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"system_settings_key_unique": {"name": "system_settings_key_unique", "nullsNotDistinct": false, "columns": ["key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.referal": {"name": "referal", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "referred_by_id": {"name": "referred_by_id", "type": "integer", "primaryKey": false, "notNull": false}, "referral_code": {"name": "referral_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"referal_user_id_users_id_fk": {"name": "referal_user_id_users_id_fk", "tableFrom": "referal", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "referal_referred_by_id_users_id_fk": {"name": "referal_referred_by_id_users_id_fk", "tableFrom": "referal", "tableTo": "users", "columnsFrom": ["referred_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.escrow_status": {"name": "escrow_status", "schema": "public", "values": ["funds_recieved", "completed", "disputed", "cancelled", "expired", "funds_released", "created"]}, "public.kyc_status": {"name": "kyc_status", "schema": "public", "values": ["pending", "approved", "rejected"]}, "public.role": {"name": "role", "schema": "public", "values": ["admin", "user", "super_admin"]}, "public.status": {"name": "status", "schema": "public", "values": ["completed", "accepted", "disputed", "cancelled", "expired", "declined", "pending", "failed"]}, "public.payment_method_type": {"name": "payment_method_type", "schema": "public", "values": ["mobile_money", "master_card", "visa_card"]}, "public.payment_direction": {"name": "payment_direction", "schema": "public", "values": ["to_escrow", "to_reciver", "from_escrow"]}, "public.payment_status": {"name": "payment_status", "schema": "public", "values": ["pending", "completed", "funded", "failed", "refunded", "cancelled"]}, "public.category": {"name": "category", "schema": "public", "values": ["transactions", "security_alerts", "reminders", "general_updates"]}, "public.priority": {"name": "priority", "schema": "public", "values": ["pending", "warning", "info", "active", "completed", "declined", "error", "action_required"]}, "public.type": {"name": "type", "schema": "public", "values": ["personal", "broadcast", "admin"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}