/* eslint-disable @typescript-eslint/no-unsafe-call */
import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import helmet from 'helmet';
import compression from 'compression';
import { ResponseInterceptor } from './common/interceptors/resposne.interceptor';
import { GlobalFilter } from './common/filters/global.filter';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    rawBody: true,
  });

  app.enableCors();
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
    }),
  );

  app.useGlobalInterceptors(new ResponseInterceptor());
  app.useGlobalFilters(new GlobalFilter());
  app.setGlobalPrefix('api/holdex/');
  app.enableShutdownHooks();

  app.enableVersioning();

  app.use(helmet());
  app.use(compression());

  const config = new DocumentBuilder()
    .setTitle('Hold EX')
    .setDescription('Hold EX is an escrow platform for Cameroon')
    .setVersion('1.0')
    .addBearerAuth()
    .setContact('Hold X', 'https://holdx.com', '<EMAIL>')
    .setLicense('MIT', 'https://opensource.org/licenses/MIT')
    .addServer('https://backend-h32u.onrender.com')
    .addServer('http://localhost:9999')
    .build();
  const documentFactory = () => SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/holdex/docs', app, documentFactory);

  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
