import { ApiProperty } from '@nestjs/swagger';
import {
  IsDateString,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateTransactionDto {
  @ApiProperty({
    example: 'Transaction Title',
    description: 'Transaction title',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    example: 'Transaction Description',
    description: 'Transaction description',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    example: 'Transaction Aggrement',
    description: 'Transaction aggrement',
  })
  @IsOptional()
  aggrement: string;

  @ApiProperty({
    example: 'Transaction Category',
    description: 'Transaction category',
  })
  @IsString()
  @IsNotEmpty()
  category: string;

  @ApiProperty({
    example: '2021-01-01T00:00:00.000Z',
    description: 'Transaction dead line',
  })
  @IsDateString()
  @IsOptional()
  deadLine: string;

  @ApiProperty({ example: '1', description: 'Seller ID' })
  @IsString()
  @IsNotEmpty()
  sellerId: string;

  @ApiProperty({
    example: '1',
    description: 'Payment method ID',
  })
  @IsString()
  @IsNotEmpty()
  paymentMethodId: string;

  @ApiProperty({
    example: '1000',
    description: 'Transaction amount',
  })
  @IsString()
  @IsNotEmpty()
  amount: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  fee: string;
}
