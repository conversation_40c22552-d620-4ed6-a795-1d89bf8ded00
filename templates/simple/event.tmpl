import 'dart:async';
import 'dart:developer' as developer;

import 'package:${appName}${relative}/index.dart';
import 'package:meta/meta.dart';

@immutable
abstract class ${upperName}Event {
  Stream<${upperName}State> applyAsync(
      {${upperName}State currentState, ${upperName}Bloc bloc});
}

class Un${upperName}Event extends ${upperName}Event {
  @override
  Stream<${upperName}State> applyAsync({${upperName}State? currentState, ${upperName}Bloc? bloc}) async* {
    yield Un${upperName}State();
  }
}

class Load${upperName}Event extends ${upperName}Event {
   
  @override
  Stream<${upperName}State> applyAsync(
      {${upperName}State? currentState, ${upperName}Bloc? bloc}) async* {
    try {
      yield Un${upperName}State();
      await Future.delayed(const Duration(seconds: 1));
      yield In${upperName}State('Hello world');
    } catch (_, stackTrace) {
      developer.log('$_', name: 'Load${upperName}Event', error: _, stackTrace: stackTrace);
      yield Error${upperName}State( _.toString());
    }
  }
}
