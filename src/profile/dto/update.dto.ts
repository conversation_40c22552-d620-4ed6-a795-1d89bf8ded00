import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class UpdateProfileDto {
  @ApiProperty({ example: '<PERSON>', description: 'First name' })
  @IsString()
  @IsOptional()
  firstName?: string;
  @ApiProperty({ example: 'Doe', description: 'Last name' })
  @IsString()
  @IsOptional()
  lastName?: string;
  @ApiProperty({ example: 'Hello, I am <PERSON>', description: 'B<PERSON>' })
  @IsString()
  @IsOptional()
  bio?: string;
  @ApiProperty({
    example: 'https://example.com/avatar.jpg',
    description: 'Avatar URL',
  })
  @IsString()
  @IsOptional()
  avatar?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ example: '+1234567890', description: 'Phone number' })
  phone?: string;
}
