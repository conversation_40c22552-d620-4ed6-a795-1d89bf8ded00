import { Injectable, Inject } from '@nestjs/common';
import { eq, sql, count } from 'drizzle-orm';
import {
  escrow,
  Escrow,
  NewEscrow,
  Transaction,
  transactions,
} from '../schemas';
import {
  IEscrowRepository,
  PaginatedResponse,
  PaginationQuery,
} from '../interfaces';
import { DATABASE_CONNECTION } from 'src/configs';
import * as schema from '../schemas/escrow';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

@Injectable()
export class EscrowRepository implements IEscrowRepository {
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}
  async updateByTransactionId(
    transactionId: number,
    escrowData: Partial<Escrow>,
  ) {
    const result = await this.db
      .update(escrow)
      .set({
        ...escrowData,
      })
      .where(eq(escrow.transactionId, transactionId))
      .returning();

    return result[0] || null;
  }

  async income(): Promise<{ total: number; average: number }> {
    const result = await this.db
      .select({
        total: sql<number>`sum(${escrow.fee})`,
        average: sql<number>`avg(${escrow.fee})`,
      })
      .from(escrow)
      .where(eq(escrow.status, 'completed'));

    return result[0] || { total: 0, average: 0 };
  }

  async stats(): Promise<{
    fialed: number;
    successful: number;
    pending: number;
    total: number;
    disputed: number;
    cancelled: number;
    expired: number;
  }> {
    const result = await this.db
      .select({
        fialed: count(),
        successful: count(),
        pending: count(),
        disputed: count(),
        cancelled: count(),
        expired: count(),
        total: count(),
      })
      .from(escrow)
      .groupBy(escrow.status);

    return result[0] as {
      fialed: number;
      successful: number;
      disputed: number;
      cancelled: number;
      expired: number;
      pending: number;
      total: number;
    };
  }

  async findAll(query: PaginationQuery): Promise<PaginatedResponse<any>> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;

    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(escrow);

    const data = await this.db
      .select({
        escrow,
        transaction: transactions,
      })
      .from(escrow)
      .leftJoin(transactions, eq(escrow.transactionId, transactions.id))
      .limit(limit)
      .offset(offset)
      .orderBy(escrow.createdAt);

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async findByTransactionId(transactionId: number): Promise<{
    escrow: Escrow;
    transaction: Transaction | null;
  } | null> {
    const result = await this.db
      .select({
        escrow,
        transaction: transactions,
      })
      .from(escrow)
      .leftJoin(transactions, eq(escrow.transactionId, transactions.id))
      .where(eq(escrow.transactionId, transactionId))
      .limit(1);

    return result[0] || null;
  }

  async findById(
    id: number,
  ): Promise<{ escrow: Escrow; transaction: Transaction | null } | null> {
    const result = await this.db
      .select({
        escrow,
        transaction: transactions,
      })
      .from(escrow)
      .leftJoin(transactions, eq(escrow.transactionId, transactions.id))
      .where(eq(escrow.id, id))
      .limit(1);

    return result[0] || null;
  }
  async create(escrowData: NewEscrow): Promise<Escrow> {
    const result = await this.db
      .insert(escrow)
      .values({ ...escrowData })
      .returning();

    return result[0];
  }

  async update(
    id: number,
    escrowData: Partial<Escrow>,
  ): Promise<Escrow | null> {
    const result = await this.db
      .update(escrow)
      .set({
        ...escrowData,
      })
      .where(eq(escrow.id, id))
      .returning();

    return result[0] || null;
  }

  async delete(id: number): Promise<boolean> {
    const result = await this.db
      .delete(escrow)
      .where(eq(escrow.id, id))
      .returning();

    return result.length > 0;
  }
}
