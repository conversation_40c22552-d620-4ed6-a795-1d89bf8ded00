import { <PERSON>du<PERSON> } from '@nestjs/common';
import { KycService } from './kyc.service';
import { KycController } from './kyc.controller';
import {
  FileRepository,
  KycRepository,
  UserRepository,
} from 'src/common/repository';
import { EmailsService } from '../emails/emails.service';
import { KycNotificationService } from './kyc-notification.service';
import { AdminNotification } from 'src/webpush-notifications/admin-notifiction.gateway';

@Module({
  providers: [
    KycService,
    KycRepository,
    EmailsService,
    UserRepository,
    FileRepository,
    KycNotificationService,
    AdminNotification,
  ],
  controllers: [KycController],
})
export class KycModule {}
