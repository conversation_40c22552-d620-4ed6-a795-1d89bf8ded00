
import 'dart:async';
import 'package:${appName}${relative}/index.dart';

class ${upperName}Provider {

  Future<List<${upperName}Model>?> fetchAsync(String? id) async {
    // write logic here to send request to server
    if (id == null) {
      return null;
    }
    return [${upperName}Model(name: id)];
  }


  Future<List<${upperName}Model>?> addMore(List<${upperName}Model>? now) async {
    // write logic here to send request to server
    final result = [
      ...(now ?? <${upperName}Model>[]),
      ${upperName}Model(name: now?.length.toString() ?? '0')
    ];
    return result;
  }

}

