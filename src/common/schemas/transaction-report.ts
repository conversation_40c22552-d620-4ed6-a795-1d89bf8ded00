import {
  pgTable,
  serial,
  integer,
  text,
  timestamp,
  pgEnum,
} from 'drizzle-orm/pg-core';
import { users } from './users';
import { transactions } from './transaction';
import { relations } from 'drizzle-orm';

export const reportStatusEnum = pgEnum('report_status', [
  'pending',
  'reviewed',
  'resolved',
  'dismissed',
]);

export const reports = pgTable('reports', {
  id: serial('id').primaryKey(),
  reporterId: integer('reporter_id')
    .notNull()
    .references(() => users.id),
  transactionId: integer('transaction_id')
    .notNull()
    .references(() => transactions.id),
  platform: text('platdorm').notNull(),
  description: text('description'),
  status: reportStatusEnum('status').notNull().default('pending'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export type Report = typeof reports.$inferSelect;
export type NewReport = typeof reports.$inferInsert;

export const reportRelations = relations(reports, ({ one }) => ({
  reporter: one(users, {
    fields: [reports.reporterId],
    references: [users.id],
  }),
  transaction: one(transactions, {
    fields: [reports.transactionId],
    references: [transactions.id],
  }),
}));
